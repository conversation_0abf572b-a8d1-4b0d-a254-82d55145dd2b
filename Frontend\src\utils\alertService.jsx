import React from 'react';
import { createRoot } from 'react-dom/client';
import Modal from '../components/common/Modal';

// Create a container for the modal
let modalContainer = null;
let modalRoot = null;

/**
 * Initialize the modal container
 */
const initializeModalContainer = () => {
  if (!modalContainer) {
    modalContainer = document.createElement('div');
    modalContainer.id = 'modal-container';
    document.body.appendChild(modalContainer);
    modalRoot = createRoot(modalContainer);
  }
};

/**
 * Alert component
 */
const AlertComponent = ({ message, type, title, onClose }) => {
  // Default title based on type if not provided
  const defaultTitle = title || (
    type === 'success' ? 'Success' :
    type === 'error' ? 'Error' :
    type === 'warning' ? 'Warning' :
    'Information'
  );

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={defaultTitle}
      type={type}
      buttons={[
        {
          text: 'OK',
          onClick: onClose,
          variant: 'primary'
        }
      ]}
      size="small"
    >
      <p>{message}</p>
    </Modal>
  );
};

/**
 * Confirmation component
 */
const ConfirmComponent = ({ message, type, title, onConfirm, onCancel }) => {
  // Default title if not provided
  const defaultTitle = title || 'Confirmation';

  return (
    <Modal
      isOpen={true}
      onClose={onCancel}
      title={defaultTitle}
      type={type || 'warning'}
      buttons={[
        {
          text: 'Cancel',
          onClick: onCancel,
          variant: 'secondary'
        },
        {
          text: 'Confirm',
          onClick: onConfirm,
          variant: type === 'error' ? 'danger' : 'primary'
        }
      ]}
      size="small"
    >
      <p>{message}</p>
    </Modal>
  );
};

/**
 * Show an alert modal
 *
 * @param {String} message - The message to display
 * @param {String} type - The type of alert (info, success, error, warning)
 * @param {String} title - Optional title for the alert
 * @returns {Promise} - Resolves when the alert is closed
 */
export const showAlert = (message, type = 'info', title = '') => {
  initializeModalContainer();

  return new Promise((resolve) => {
    const handleClose = () => {
      // Unmount the component
      modalRoot.render(null);
      resolve();
    };

    // Render the alert component
    modalRoot.render(
      <AlertComponent
        message={message}
        type={type}
        title={title}
        onClose={handleClose}
      />
    );
  });
};

/**
 * Show a confirmation modal
 *
 * @param {String} message - The message to display
 * @param {String} type - The type of confirmation (info, success, error, warning)
 * @param {String} title - Optional title for the confirmation
 * @returns {Promise<boolean>} - Resolves with true if confirmed, false if cancelled
 */
export const showConfirm = (message, type = 'warning', title = '') => {
  initializeModalContainer();

  return new Promise((resolve) => {
    const handleConfirm = () => {
      modalRoot.render(null);
      resolve(true);
    };

    const handleCancel = () => {
      modalRoot.render(null);
      resolve(false);
    };

    // Render the confirm component
    modalRoot.render(
      <ConfirmComponent
        message={message}
        type={type}
        title={title}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
    );
  });
};

// Shorthand functions for different alert types
export const showInfoAlert = (message, title = '') => showAlert(message, 'info', title);
export const showSuccessAlert = (message, title = '') => showAlert(message, 'success', title);
export const showErrorAlert = (message, title = '') => showAlert(message, 'error', title);
export const showWarningAlert = (message, title = '') => showAlert(message, 'warning', title);

// Export a function to replace window.alert
export const alert = (message) => showAlert(message);

// Export a function to replace window.confirm
export const confirm = (message) => showConfirm(message);

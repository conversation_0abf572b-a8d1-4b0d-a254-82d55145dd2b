import React, { useState, useEffect } from "react";
import { FaClock, FaCog, FaCheckCircle, FaTimesCircle, FaHourglassHalf, FaPencilAlt } from "react-icons/fa";

const CourseSettingsStep = ({ courseData, updateCourseData }) => {
    // Parse existing duration if available
    const parseDuration = (durationStr) => {
        if (!durationStr) return { timeValue: "", timeUnit: "hours" };

        // Try to parse existing duration string
        const hoursMatch = durationStr.match(/(\d+).*?hour/i);
        const weeksMatch = durationStr.match(/(\d+).*?week/i);

        if (hoursMatch) {
            return { timeValue: hoursMatch[1], timeUnit: "hours" };
        } else if (weeksMatch) {
            return { timeValue: weeksMatch[1], timeUnit: "weeks" };
        }

        return { timeValue: "", timeUnit: "hours" };
    };

    const { timeValue, timeUnit } = parseDuration(courseData.duration);

    // Local state for form fields
    const [localData, setLocalData] = useState({
        timeValue: timeValue,
        timeUnit: timeUnit,
        duration: courseData.duration || "",
        status: courseData.status ?? 1, // 1=active, 0=inactive
        isDraft: courseData.isDraft ?? true // true=draft, false=published
    });

    // Handle simple input changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setLocalData(prev => ({ ...prev, [name]: value }));

        // If changing time value or unit, update the duration string
        if (name === 'timeValue' || name === 'timeUnit') {
            const newTimeValue = name === 'timeValue' ? value : localData.timeValue;
            const newTimeUnit = name === 'timeUnit' ? value : localData.timeUnit;

            // Only update if we have a valid time value
            if (newTimeValue && !isNaN(newTimeValue) && parseInt(newTimeValue) > 0) {
                const newDuration = `${newTimeValue} ${newTimeUnit}`;
                setLocalData(prev => ({ ...prev, duration: newDuration }));
                updateCourseData({ duration: newDuration });
            }
        } else {
            updateCourseData({ [name]: value });
        }
    };

    // Toggle course status (active/inactive) and publication status
    const toggleStatus = () => {
        const newStatus = localData.status === 1 ? 0 : 1;
        // When toggling to active, also set isDraft to false (published)
        // When toggling to inactive, set isDraft to true (draft)
        const newIsDraft = newStatus === 0;

        setLocalData(prev => ({
            ...prev,
            status: newStatus,
            isDraft: newIsDraft
        }));

        updateCourseData({
            status: newStatus,
            isDraft: newIsDraft
        });
    };

    // Time unit options
    const timeUnitOptions = [
        { value: "hours", label: "Hours" },
        { value: "weeks", label: "Weeks" }
    ];

    return (
        <div className="course-settings-step">
            <h2>Course Settings</h2>
            <p className="step-description">
                Configure important settings for your course to help students understand what to expect.
            </p>

            <div className="settings-card">
                <div className="settings-card-header">
                    <div className="settings-card-icon">
                        <FaClock />
                    </div>
                    <h3 className="settings-card-title">Time Commitment</h3>
                </div>
                <p className="field-hint">
                    Help students understand how much time they should set aside to complete this course.
                </p>
                <div className="form-group">
                    <label htmlFor="timeValue">
                        Course Duration
                    </label>
                    <div className="time-commitment-container">
                        <div className="time-value-input">
                            <input
                                type="number"
                                id="timeValue"
                                name="timeValue"
                                value={localData.timeValue}
                                onChange={handleInputChange}
                                placeholder="Enter time"
                                min="1"
                                className="input-field"
                            />
                        </div>
                        <div className="time-unit-selector">
                            <select
                                id="timeUnit"
                                name="timeUnit"
                                value={localData.timeUnit}
                                onChange={handleInputChange}
                                className="select-field"
                            >
                                {timeUnitOptions.map(option => (
                                    <option key={option.value} value={option.value}>
                                        {option.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                    <p className="field-hint">
                        Approximate time it takes to complete the course.
                    </p>

                    {localData.duration && (
                        <div className="duration-badge">
                            <FaHourglassHalf />
                            <span>Duration: {localData.duration}</span>
                        </div>
                    )}
                </div>
            </div>

            <div className="settings-card">
                <div className="settings-card-header">
                    <div className="settings-card-icon">
                        <FaCog />
                    </div>
                    <h3 className="settings-card-title">Course Visibility & Publication</h3>
                </div>
                <p className="field-hint">
                    Control whether your course is visible to students and its publication status.
                </p>
                <div className="toggle-group">
                    <div className="toggle-item">
                        <div className="toggle-info">
                            <h3>
                                Course Status
                                <span className={`status-badge ${localData.status === 1 ? 'active' : 'inactive'}`}>
                                    {localData.status === 1
                                        ? <><FaCheckCircle /> Active & Published</>
                                        : <><FaPencilAlt /> Inactive & Draft</>
                                    }
                                </span>
                            </h3>
                            <p>
                                {localData.status === 1
                                    ? "Course is active, published, and available to enrolled students."
                                    : "Course is inactive, in draft mode, and hidden from students."}
                            </p>
                        </div>
                        <div className="toggle-wrapper">
                            <label className="schooljxs-toggle">
                                <input
                                    type="checkbox"
                                    checked={localData.status === 1}
                                    onChange={toggleStatus}
                                />
                                <span className="slider"></span>
                            </label>
                            <div className="toggle-label-text">
                                {localData.status === 1 ? 'Active & Published' : 'Inactive & Draft'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CourseSettingsStep;
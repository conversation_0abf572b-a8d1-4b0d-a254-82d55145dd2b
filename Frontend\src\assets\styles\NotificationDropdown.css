/* Notification Dropdown */
.notification-dropdown {
  position: relative;
  display: inline-block;
}

.notification-btn {
  background: none;
  border: none;
  color: var(--white);
  font-size: 1.2rem;
  cursor: pointer;
  position: relative;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.notification-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  max-height: 400px;
  background-color: var(--bg-white);
  border-radius: var(--border-small-radius);
  box-shadow: var(--box-shadow);
  z-index: var(--z-index-tooltip);
  overflow: hidden;
  margin-top: 10px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--border-gray);
}

.notification-header h3 {
  margin: 0;
  font-size: var(--heading6);
  color: var(--text-primary);
}

.mark-all-read-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: var(--smallfont);
  cursor: pointer;
  padding: 5px;
}

.mark-all-read-btn:hover {
  text-decoration: underline;
}

.notification-list {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-gray);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.notification-item:hover {
  background-color: var(--bg-gray);
}

.notification-item.unread {
  background-color: rgba(var(--primary-color-rgb), 0.05);
}

.notification-item.unread:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
}

.notification-avatar {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.notification-content {
  flex: 1;
}

.notification-content p {
  margin: 0 0 5px;
  font-size: var(--basefont);
  color: var(--text-color);
  line-height: 1.4;
}

.notification-time {
  font-size: var(--extrasmallfont);
  color: var(--text-gray);
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-left: 10px;
}

.notification-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  padding: 5px;
  color: var(--text-gray);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-action-btn:hover {
  background-color: var(--bg-gray);
}

.notification-action-btn.read-btn:hover {
  color: var(--primary-color);
}

.notification-action-btn.delete-btn:hover {
  color: #ff4757;
}

.no-notifications {
  padding: 20px;
  text-align: center;
  color: var(--text-gray);
  font-size: var(--basefont);
}

.notification-loading {
  padding: 20px;
  text-align: center;
  color: var(--text-gray);
  font-size: var(--basefont);
}

/* Responsive styles */
@media (max-width: 768px) {
  .notification-menu {
    width: 280px;
    right: -10px;
  }
}

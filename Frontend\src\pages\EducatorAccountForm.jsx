import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  createEducatorThunk as createUniversityEducatorThunk,
  updateEducatorThunk as updateUniversityEducatorThunk,
  getEducatorsThunk as getUniversityEducatorsThunk,
} from "../redux/university/universitySlice";
import {
  createEducatorThunk as createAdminEducatorThunk,
  updateEducatorThunk as updateAdminEducatorThunk,
  getEducatorsThunk as getAdminEducatorsThunk,
  getUniversitiesThunk,
} from "../redux/admin/adminSlice";
import { getRolesThunk } from "../redux/role/roleSlice";
import "../assets/styles/EducatorAccountForm.css";
import { FaArrowLeft } from "react-icons/fa";
import LoadingSpinner from "../components/common/LoadingSpinner";

// INDIAN_STATES removed since address fields are no longer used for educators

const EducatorAccountForm = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const educatorData = location.state?.educator || null;
  const isEditMode = !!educatorData;

  // Get user, roles, and universities from Redux store
  const { user } = useSelector((state) => state.auth);
  const { roles } = useSelector((state) => state.role);
  const { universities, loading: universitiesLoading } = useSelector(
    (state) => state.admin
  );

  // Determine if user is admin
  const isAdmin = user?.role === "admin";

  // Filter out predefined roles from the dropdown

  // Predefined roles to exclude from the dropdown
  const predefinedRoles = [
    "admin",
    "staff",
    "university",
    "educator",
    "super admin",
    "iim staff",
    "school admin",
    "school",
    "super_admin",
    "iim_staff",
    "school_admin",
  ];

  // Filter out predefined roles from the roles list
  const filteredRoles = roles
    ? roles.filter((role) => {
      // Check if the role has a name property
      if (!role.name) {
        return false;
      }

      // Only include roles that are not in the predefined list
      const roleLowerCase = role.name.toLowerCase().trim();

      // Check if this role name (or a variation) is in our exclude list
      const isExcluded = predefinedRoles.some(
        (predefinedRole) =>
          roleLowerCase === predefinedRole ||
          roleLowerCase.replace(/[_\s-]/g, "") ===
          predefinedRole.replace(/[_\s-]/g, "")
      );

      // Only include custom roles (not in the predefined list)
      return !isExcluded;
    })
    : [];

  // Debug check for "New Role"
  const hasNewRole = filteredRoles.some(
    (role) =>
      role.name.toLowerCase().includes("new") &&
      role.name.toLowerCase().includes("role")
  );

  // Fetch roles and universities on component mount
  useEffect(() => {
    dispatch(getRolesThunk());
    dispatch(getUniversitiesThunk());
  }, [dispatch]);

  // Log detailed role information when roles change
  useEffect(() => {
    if (roles && roles.length > 0) {
      roles.forEach((role) => {
      });
    }
  }, [roles]);

  // Log roles and universities when they change
  useEffect(() => {
  }, [roles, universities]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState({});

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    universityId: "", // Changed from schoolName to universityId for dropdown selection
    category: "",
    email: "",
    phoneNumber: "",
    // address field removed for educators
    zipcode: "",
    state: "",
    roleId: "",
    profileImage: null,
    profileImageUrl: "",
    status: 1,
  });

  // Check if roleId matches any role when formData is initialized
  // This useEffect must be placed after formData state initialization
  useEffect(() => {
    // If in edit mode and we have both educatorData and roles, check if roleId matches any role
    if (
      isEditMode &&
      educatorData &&
      roles &&
      roles.length > 0 &&
      formData.roleId
    ) {
      const matchingRole = roles.find((role) => role._id === formData.roleId);
    }
  }, [roles, isEditMode, educatorData, formData.roleId]);

  useEffect(() => {
    if (isEditMode && educatorData) {
      // Strip the '+91' prefix from phone number if exists
      const phoneNumber = educatorData.mobile
        ? educatorData.mobile.replace(/^\+91\s*/, "").trim()
        : "";

      // If roleRef is an object (populated), extract the _id
      let roleId = "";

      if (educatorData.roleRef) {
        if (typeof educatorData.roleRef === "object") {
          roleId = educatorData.roleRef._id;
        } else {
          roleId = educatorData.roleRef;
        }
      }

      // Check if avatar is directly on the educator object or in the profile object
      let avatarUrl =
        educatorData.avatar ||
        (educatorData.profile && educatorData.profile.avatar) ||
        "";

      // If the avatar URL is a full URL (starts with http), use it directly for display
      // Otherwise, it's a relative path, so prepend the base URL (without /api)
      if (avatarUrl && !avatarUrl.startsWith("http")) {
        avatarUrl = `${import.meta.env.VITE_API_URL.replace(
          "/api",
          ""
        )}${avatarUrl}`;
      }

      // Find university ID if we have a school name
      let universityId = "";
      if (educatorData.university) {
        // If university is an object with _id
        if (
          typeof educatorData.university === "object" &&
          educatorData.university._id
        ) {
          universityId = educatorData.university._id;
        }
        // If university is just the ID
        else if (typeof educatorData.university === "string") {
          universityId = educatorData.university;
        }
      }
      // If no direct university reference, try to find by name
      else if (educatorData.school && universities && universities.length > 0) {
        const matchingUniversity = universities.find(
          (uni) => uni.name.toLowerCase() === educatorData.school.toLowerCase()
        );
        if (matchingUniversity) {
          universityId = matchingUniversity._id;
        }
      }

      setFormData({
        firstName: educatorData.firstName || "",
        lastName: educatorData.lastName || "",
        universityId: universityId,
        category: educatorData.category || "",
        email: educatorData.email || "",
        phoneNumber: phoneNumber,
        // address field removed for educators
        zipcode: educatorData.zipcode || "",
        state: educatorData.state || "",
        roleId: roleId, // Use the extracted roleId
        profileImageUrl: avatarUrl,
        status: educatorData.status ? 1 : 0,
      });

    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEditMode, educatorData, universities]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData({
        ...formData,
        profileImage: file,
        profileImageUrl: URL.createObjectURL(file),
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setFormErrors({});
    setIsSubmitting(true);

    // Validate required fields
    if (!formData.firstName || formData.firstName.trim() === "") {
      setFormErrors({ ...formErrors, firstName: "First name is required" });
      setIsSubmitting(false);
      return;
    }

    if (!formData.lastName || formData.lastName.trim() === "") {
      setFormErrors({ ...formErrors, lastName: "Last name is required" });
      setIsSubmitting(false);
      return;
    }

    // Create FormData object for file upload
    const formDataObj = new FormData();

    // Append text data
    formDataObj.append("firstName", formData.firstName);
    formDataObj.append("lastName", formData.lastName);
    formDataObj.append("email", formData.email);

    // Format phone number properly - ensure it's not empty
    if (!formData.phoneNumber || formData.phoneNumber.trim() === "") {
      setFormErrors({ ...formErrors, phoneNumber: "Phone number is required" });
      setIsSubmitting(false);
      return;
    }

    // Add +91 prefix only if it doesn't already have it
    const phoneNumber = formData.phoneNumber.trim();
    const formattedPhoneNumber = phoneNumber.startsWith("+91")
      ? phoneNumber
      : "+91 " + phoneNumber;

    formDataObj.append("phoneNumber", formattedPhoneNumber);
    // address field removed for educators
    formDataObj.append("zipcode", formData.zipcode);
    formDataObj.append("state", formData.state);
    formDataObj.append("status", Number(formData.status));
    formDataObj.append("category", formData.category); // Add category field

    // Validate that a university is selected
    if (!formData.universityId) {
      setFormErrors({
        ...formErrors,
        universityId: "Please select a School/University",
      });
      setIsSubmitting(false);
      return;
    }

    // Find the selected university to get its name
    const selectedUniversity = universities.find(
      (uni) => uni._id === formData.universityId
    );
    if (selectedUniversity) {
      formDataObj.append("schoolName", selectedUniversity.name); // Add school/university name
      formDataObj.append("university", formData.universityId); // Add university ID
    } else {
      setFormErrors({
        ...formErrors,
        universityId: "Selected university not found",
      });
      setIsSubmitting(false);
      return;
    }

    // No password field needed

    // Add roleId if selected, otherwise set default role to 'educator'
    if (formData.roleId) {
      formDataObj.append("roleId", formData.roleId); // Backend expects roleId in the request
    } else {
      formDataObj.append("role", "educator");
    }

    // Add profile image if selected
    if (formData.profileImage) {
      formDataObj.append("profileImage", formData.profileImage);
    } else {
    }

    if (isEditMode) {
      // Update existing educator
      // Use the appropriate thunk based on user role
      const updateThunk = isAdmin
        ? updateAdminEducatorThunk
        : updateUniversityEducatorThunk;
      const getEducatorsThunk = isAdmin
        ? getAdminEducatorsThunk
        : getUniversityEducatorsThunk;

      dispatch(
        updateThunk({
          id: educatorData.id,
          formData: formDataObj,
        })
      )
        .unwrap()
        .then(() => {
          // Refresh educators list
          dispatch(getEducatorsThunk());
          // Navigate back
          navigate(-1);
        })
        .catch((error) => {
          console.error("Error updating educator:", error);
          // Handle different error formats
          if (error.errors) {
            setFormErrors(error.errors);
          } else if (error.message) {
            // Handle MongoDB duplicate key errors
            if (
              error.message.includes("duplicate key error") &&
              error.message.includes("phoneNumber")
            ) {
              setFormErrors({
                ...formErrors,
                phoneNumber:
                  "This phone number is already in use. Please use a different phone number.",
              });
            } else {
              setFormErrors({
                ...formErrors,
                general: error.message,
              });
            }
          } else {
            setFormErrors({
              ...formErrors,
              general: "An error occurred. Please try again.",
            });
          }
          setIsSubmitting(false);
        });
    } else {
      // Create new educator - use the appropriate thunk based on user role
      const createThunk = isAdmin
        ? createAdminEducatorThunk
        : createUniversityEducatorThunk;

      dispatch(createThunk(formDataObj))
        .unwrap()
        .then(() => {
          // Refresh educators list
          // Use the appropriate thunk based on user role
          const getEducatorsThunk = isAdmin
            ? getAdminEducatorsThunk
            : getUniversityEducatorsThunk;
          dispatch(getEducatorsThunk());
          // Navigate back to educators list
          navigate("/dashboard/admin/educators");
        })
        .catch((error) => {
          console.error("Error creating educator:", error);
          // Handle different error formats
          if (error.errors) {
            setFormErrors(error.errors);
          } else if (error.message) {
            // Handle MongoDB duplicate key errors
            if (
              error.message.includes("duplicate key error") &&
              error.message.includes("phoneNumber")
            ) {
              setFormErrors({
                ...formErrors,
                phoneNumber:
                  "This phone number is already in use. Please use a different phone number.",
              });
            } else {
              setFormErrors({
                ...formErrors,
                general: error.message,
              });
            }
          } else {
            setFormErrors({
              ...formErrors,
              general: "An error occurred. Please try again.",
            });
          }
          setIsSubmitting(false);
        });
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  // Show loading spinner when universities are being fetched
  if (universitiesLoading || !universities) {
    return <LoadingSpinner />;
  }

  return (
    <div className="educator-form-container">
      <div className="form-header">
        <button className="back-button" onClick={handleCancel}>
          <FaArrowLeft />
        </button>
        <h1>
          {isEditMode ? "Edit Educator Account" : "Create Educator Account"}
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="educator-form">
        {/* General Information Section */}
        <div className="form-section">
          <h2>General Information</h2>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="firstName">First Name</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                placeholder="Enter First Name"
                required
              />
              {formErrors.firstName && (
                <div className="error-message">{formErrors.firstName}</div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="lastName">Last Name</label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                placeholder="Enter Last Name"
                required
              />
              {formErrors.lastName && (
                <div className="error-message">{formErrors.lastName}</div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="category">Category</label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Category</option>
                <option value="School">School</option>
                <option value="University">University</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="status">Account Status</label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                required
              >
                <option value={1}>Active</option>
                <option value={0}>Inactive</option>
              </select>
            </div>
          </div>
        </div>

        {/* Contact Information Section */}
        <div className="form-section">
          <h2>Contact Information</h2>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="email">Email Address</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter Email Address"
                required
              />
              {formErrors.email && (
                <div className="error-message">{formErrors.email}</div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="phoneNumber">Phone Number</label>
              <div className="phone-input-wrapper">
                <span className="phone-prefix">+91</span>
                <input
                  type="tel"
                  id="phoneNumber"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  placeholder="Enter Phone Number"
                  required
                />
              </div>
              {formErrors.phoneNumber && (
                <div className="error-message">{formErrors.phoneNumber}</div>
              )}
            </div>
          </div>
        </div>

        {/* Professional Details Section */}
        <div className="form-section">
          <h2>Professional Details</h2>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="universityId">School/University</label>
              <select
                id="universityId"
                name="universityId"
                value={formData.universityId}
                onChange={handleInputChange}
                required
              >
                <option value="">Select School/University</option>
                {universities && universities.length > 0 ? (
                  universities.map((university) => (
                    <option key={university._id} value={university._id}>
                      {university.name}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>
                    Loading universities...
                  </option>
                )}
              </select>
              {formErrors.universityId && (
                <div className="error-message">{formErrors.universityId}</div>
              )}
            </div>

            {/* {isAdmin && filteredRoles.length >= 1 && (
              <div className="form-group">
                <label htmlFor="roleId">Role</label>
                <select
                  id="roleId"
                  name="roleId"
                  value={formData.roleId}
                  onChange={handleInputChange}
                >
                  <option value="">Select Role</option>
                  {filteredRoles.map((role) => (
                    <option key={role._id} value={role._id}>
                      {role.name}
                    </option>
                  ))}
                </select>
                {formErrors.roleId && (
                  <div className="error-message">{formErrors.roleId}</div>
                )}
              </div>
            )} */}
          </div>
        </div>

        {/* Address Information Section removed for educators */}

        {/* Profile Image Section */}
        <div className="form-section">
          <h2>Profile Image</h2>
          <div className="profile-section">
            <div className="profile-preview">
              {formData.profileImageUrl ? (
                <img
                  src={formData.profileImageUrl}
                  alt="Profile"
                  className="profile-preview-image"
                  onError={(e) => {
                    console.error("Error loading image:", e);
                    e.target.onerror = null;
                    e.target.src =
                      "https://randomuser.me/api/portraits/men/1.jpg";
                  }}
                />
              ) : (
                <div className="profile-placeholder"></div>
              )}
            </div>
            <div className="profile-actions">
              <button
                type="button"
                className="upload-photo-btn"
                onClick={() => document.getElementById("profileImage").click()}
              >
                Upload Photo
              </button>
              <p className="upload-hint">
                Recommended size: 300x300px. Max file size: 5MB
              </p>
              <input
                type="file"
                id="profileImage"
                name="profileImage"
                accept="image/*"
                onChange={handleImageUpload}
                style={{ display: "none" }}
              />
            </div>
          </div>
        </div>

        <div className="form-actions">
          <button
            type="button"
            className="cancel-btn"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button type="submit" className="submit-btn" disabled={isSubmitting}>
            {isSubmitting ? (
              <span className="loading-spinner-small"></span>
            ) : isEditMode ? (
              "Update"
            ) : (
              "Submit"
            )}
          </button>
        </div>

        {/* General error message */}
        {formErrors.general && (
          <div className="error-message general-error">
            {formErrors.general}
          </div>
        )}
      </form>
    </div>
  );
};

export default EducatorAccountForm;

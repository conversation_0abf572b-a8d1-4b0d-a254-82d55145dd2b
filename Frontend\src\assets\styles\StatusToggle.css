/* Status Toggle Styles */
.status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-cell-no-toggle {
  justify-content: flex-start;
}

.status-indicator {
  width: 36px;
  height: 20px;
  border-radius: 20px;
  background-color: #e5e7eb;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
  flex-shrink: 0;
}

.status-indicator::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
}

.status-indicator.active {
  background-color: var(--primary-color);
}

.status-indicator.active::before {
  transform: translateX(16px);
}

.text-green-600 {
  color: #16a34a;
  font-size: 14px;
  font-weight: 500;
}

.text-red-600 {
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
}

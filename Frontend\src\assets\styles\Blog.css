/* Blog Page Styles */
.blog-container {
  margin: 0 auto;
  min-height: 100vh;
}

.blog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.blog-title {
  font-size: var(--heading4);
  color: var(--text-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.blog-title-icon {
  color: var(--primary-color);
}

.blog-actions {
  display: flex;
  gap: 10px;
}

/* Search and Filter Container */
.search-filter-container {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
}

.search-input {
  position: relative;
}

.search-input input {
  padding: 0px 16px;

  font-size: 14px;
  min-width: 250px;
  outline: none;
  transition: border-color 0.2s;
  width: 100%;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-gray);
}

.filter-select {
  padding: 10px 15px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  background-color: white;
}

.add-blog-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-small-radius);
  padding: 10px 20px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
  white-space: nowrap;
  width: fit-content;
  font-size: var(--smallfont);
}

.add-blog-btn:hover {
  background-color: var(--primary-hover-color);
}

/* Card Grid Layout Styles (for Educator view) */
.blog-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Educator view search filter container adjustments */
.blog-container .search-filter-container {
  margin-bottom: 20px;
}

.blog-card {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border-radius: var(--border-medium-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow);
}

.blog-card-image {
  width: 100%;
  height: 180px;
  object-fit: contain;
}

.blog-card-content {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.blog-card-title {
  font-size: var(--heading6);
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-primary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-card-excerpt {
  color: var(--text-gray);
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
}

.blog-card-footer {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
  color: var(--text-gray);
  font-size: var(--smallfont);
}

.blog-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.blog-card-author {
  display: flex;
  align-items: center;
  gap: 5px;
}

.blog-card-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.blog-avatar-placeholder {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
}

.blog-card-date {
  font-size: var(--extrasmallfont);
}

/* DataTable customization for blog table */
.blog-table-container .data-table-container {
  box-shadow: var(--box-shadow-light);
}

.blog-image-cell {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0px;
   overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
}

.blog-table-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-small-radius);
  object-fit: cover;
}

.blog-table-title {
  display: flex;
  flex-direction: column;
}

.blog-table-title-text {
  font-weight: 500;
  color: var(--text-primary);
}

.blog-table-excerpt {
  font-size: var(--smallfont);
  color: var(--text-gray);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-author-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.blog-author-icon {
  color: var(--text-gray);
  font-size: 16px;
}

.blog-actions-cell {
  display: flex;
  gap: 8px !important;
  justify-content: flex-end;
}

.blog-action-btn {
  background: none;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.blog-action-btn:hover {
  background-color: var(--bg-gray);
}

.blog-edit-btn {
  background-color: #ecfdf5;
  color: #059669;
}

.blog-delete-btn {
  background-color: #fef2f2;
  color: #dc2626;
}

.blog-view-btn {
  background-color: var(--primary-light-bg) !important;
  color: var(--primary-color);
}

/* Status Cell Styles */
.status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Status Indicator - Toggle Switch Style */
.status-indicator {
  width: 36px;
  height: 20px;
  border-radius: 20px;
  background-color: #e5e7eb;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
  flex-shrink: 0;
}

.status-indicator::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
}

.status-indicator.active {
  background-color: var(--primary-color);
}

.status-indicator.active::before {
  transform: translateX(16px);
}

.text-green-600 {
  color: #16a34a;
  font-size: 14px;
  font-weight: 500;
}

.text-red-600 {
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
}

/* Keep the badge style for card view */
.blog-status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--smallfont);
  font-weight: 500;
  text-align: center;
  width: 80px;
}

.blog-status-badge.published {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.blog-status-badge.draft {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

/* Blog Form */
.blog-form-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.blog-form-page-title {
  font-size: var(--heading5);
  color: var(--text-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.blog-form {
  background-color: var(--white);
  border-radius: var(--border-medium-radius);
  box-shadow: var(--box-shadow-light);
  padding: 0;
  margin: 0 auto;
  overflow: hidden;
}

/* Horizontal Layout */
.blog-form-horizontal {
  display: flex;
  flex-direction: column;
}

.blog-form-main-content {
  display: flex;
  min-height: calc(100vh - 200px);
}

.blog-form-content-column {
  flex: 1;
  padding: 30px;
  border-right: 1px solid var(--border-gray);
  overflow-y: auto;
}

.blog-form-sidebar-column {
  width: 380px;
  position: relative;
}

.blog-form-sidebar {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
}

/* Form Sections */
.blog-form-section {
  background-color: var(--bg-white);
  border-radius: var(--border-small-radius);
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid var(--border-gray);
}

.blog-form-section-title {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-gray);
}

/* Form Elements */
.blog-form-group {
  margin-bottom: 25px;
}

.blog-form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
}

.blog-form-input,
.blog-form-textarea,
.blog-form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  transition: all 0.3s ease;
}

.blog-form-input:focus,
.blog-form-textarea:focus,
.blog-form-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.blog-form-textarea {
  min-height: 150px;
  resize: vertical;
}

.blog-form-help {
  display: block;
  margin-top: 5px;
  font-size: var(--extrasmallfont);
  color: var(--text-gray);
}

/* Blog Preview Card */
.blog-preview-card {
  background-color: var(--white);
  border-radius: var(--border-small-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow-light);
}

.blog-preview-image {
  height: 160px;
  overflow: hidden;
  background-color: var(--bg-gray);
  position: relative;
}

.blog-preview-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.blog-preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-gray);
  font-size: var(--smallfont);
  background-color: var(--bg-gray);
}

.blog-preview-content {
  padding: 15px;
}

.blog-preview-title {
  font-size: var(--heading6);
  font-weight: 600;
  margin: 0 0 10px 0;
  color: var(--text-primary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-preview-description {
  color: var(--text-gray);
  font-size: var(--smallfont);
  margin: 0 0 15px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-preview-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.blog-status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: uppercase;
}

.blog-status-badge.published {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.blog-status-badge.draft {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

/* File Upload */
.blog-form-file-upload {
  width: 100%;
}

.blog-form-file-dropzone {
  border: 2px dashed var(--border-gray);
  border-radius: var(--border-small-radius);
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.blog-form-file-dropzone:hover {
  border-color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.02);
}

.blog-form-file-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
}

.blog-form-file-icon {
  margin-bottom: 10px;
  color: var(--text-gray);
}

.blog-form-file-preview {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: var(--border-small-radius);
  border: 3px solid var(--white);
  box-shadow: var(--box-shadow-light);
}

.blog-form-file-text {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.blog-form-file-text span {
  font-weight: 500;
  color: var(--text-color);
}

.blog-form-file-text small {
  color: var(--text-gray);
  font-size: var(--extrasmallfont);
}

/* Tags */
.blog-tags-input-container {
  margin-bottom: 10px;
}

.blog-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.blog-tag-item {
  display: flex;
  align-items: center;
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: var(--smallfont);
}

.blog-tag-remove {
  background: none;
  border: none;
  color: var(--primary-color);
  margin-left: 5px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  padding: 0 0 0 5px;
}

.blog-tag-remove:hover {
  color: #f44336;
}

.blog-slug-preview {
  padding: 10px;
  background-color: var(--bg-gray);
  border-radius: var(--border-small-radius);
  font-family: monospace;
  word-break: break-all;
}

/* Status Toggle */
.blog-form-status-toggle {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Rich Text Editor Styling */
.blog-form .summernote-editor-container {
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  overflow: hidden;
  transition: border-color 0.3s;
}

.blog-form .summernote-editor-container:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.blog-form .note-editor {
  border: none !important;
  box-shadow: none !important;
}

.blog-form .note-toolbar {
  background-color: var(--bg-gray) !important;
  border-bottom: 1px solid var(--border-gray) !important;
  padding: 8px !important;
}

.blog-form .note-btn {
  background-color: var(--white) !important;
  border: 1px solid var(--border-gray) !important;
  color: var(--text-color) !important;
  border-radius: var(--border-small-radius) !important;
  transition: all 0.2s !important;
}

.blog-form .note-btn:hover {
  background-color: var(--primary-light-bg) !important;
  color: var(--primary-color) !important;
}

.blog-form .note-btn.active {
  background-color: var(--primary-color) !important;
  color: var(--white) !important;
  border-color: var(--primary-color) !important;
}

.blog-form .note-editable {
  min-height: 300px !important;
  padding: 15px !important;
  font-family: var(--font-Poppins) !important;
  font-size: var(--basefont) !important;
  color: var(--text-color) !important;
  line-height: 1.6 !important;
}

/* Sticky Actions */
.blog-form-sticky-actions {
  position: sticky;
  bottom: 0;
  background-color: var(--white);
  padding: 15px;
  border-top: 1px solid var(--border-gray);
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin: 20px -20px -20px -20px;
}

.blog-form-button {
  padding: 10px 20px;
  border-radius: var(--border-small-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blog-form-submit {
  background-color: var(--primary-color);
  color: var(--white);
}

.blog-form-submit:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
}

.blog-form-cancel {
  background-color: var(--bg-gray);
  color: var(--text-color);
}

.blog-form-cancel:hover {
  background-color: var(--dark-gray);
  color: var(--white);
  transform: translateY(-2px);
}

/* Required Field Indicator */
.required {
  color: #f44336;
  margin-left: 2px;
}

/* Blog Form Responsive Styles */
@media (max-width: 1200px) {
  .blog-form-content-column {
    padding: 25px;
  }

  .blog-form-sidebar-column {
    width: 350px;
  }
}

@media (max-width: 992px) {
  .blog-form-main-content {
    flex-direction: column;
    min-height: auto;
  }

  .blog-form-content-column {
    border-right: none;
    border-bottom: 1px solid var(--border-gray);
  }

  .blog-form-sidebar-column {
    width: 100%;
  }

  .blog-form-sidebar {
    padding: 25px;
  }

  .blog-preview-card {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .blog-preview-image {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
  }

  .blog-preview-content {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .blog-form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .blog-form-content-column,
  .blog-form-sidebar {
    padding: 20px;
  }

  .blog-preview-card {
    flex-direction: column;
  }

  .blog-preview-image {
    width: 100%;
    height: 140px;
  }

  .blog-form-section {
    padding: 12px;
  }

  .blog-form-sticky-actions {
    padding: 12px;
    margin: 20px -20px -20px -20px;
  }
}

@media (max-width: 576px) {
  .blog-form-content-column,
  .blog-form-sidebar {
    padding: 15px;
  }

  .blog-form-section {
    padding: 10px;
    margin-bottom: 15px;
  }

  .blog-form-section-title {
    font-size: var(--smallfont);
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .blog-form-group {
    margin-bottom: 20px;
  }

  .blog-form-input,
  .blog-form-textarea,
  .blog-form-select {
    padding: 10px;
    font-size: var(--smallfont);
  }

  .blog-form-sticky-actions {
    flex-direction: row;
    gap: 10px;
    padding: 10px;
    margin: 15px -15px -15px -15px;
  }

  .blog-form-button {
    width: 100%;
  }
}

/* Blog Detail Styles */
.blog-detail-container {
  padding: 0;
  margin: 0 auto;
  min-height: 100vh;
  position: relative;
}

/* Sticky Header */
.blog-detail-sticky-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.blog-detail-actions {
  display: flex;
  gap: 0.75rem;
}

.blog-back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 500;
  padding: 0.5rem 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.blog-back-button:hover {
  color: var(--primary-hover-color);
  transform: translateX(-3px);
}

.blog-edit-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--primary-light-bg);
  border: none;
  border-radius: var(--border-small-radius);
  color: var(--primary-color);
  font-weight: 500;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.blog-edit-button:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

.blog-status-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  border-radius: var(--border-small-radius);
  font-weight: 500;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.blog-status-button.published {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.blog-status-button.published:hover {
  background-color: rgba(76, 175, 80, 0.2);
}

.blog-status-button.draft {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.blog-status-button.draft:hover {
  background-color: rgba(255, 152, 0, 0.2);
}

/* Layout */
.blog-detail-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 1.5rem;
  padding: 1.5rem 0rem;
}

/* Main Content */
.blog-detail-content {
  background-color: var(--white);
  border-radius: var(--border-medium-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

/* Cover Image */
.blog-detail-image-container {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.blog-detail-image {
  width: 100%;
  height: 350px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.blog-detail-image:hover {
  transform: scale(1.02);
}

/* Title and Header Content */
.blog-detail-header-content {
  padding: 2rem 2rem 1rem;
}

.blog-detail-title {
  font-size: var(--heading3);
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.blog-title-icon {
  color: var(--primary-color);
  font-size: 1.75rem;
}

/* Tags */
.blog-detail-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.blog-detail-tag-pill {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
  border-radius: 50px;
  font-size: var(--smallfont);
  font-weight: 500;
  transition: all 0.2s ease;
}

.blog-detail-tag-pill:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

/* Author and Date */
.blog-detail-author-date {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 0 2rem 1.5rem;
  color: var(--text-gray);
  font-size: var(--smallfont);
  border-bottom: 1px solid var(--border-gray);
}

.blog-detail-author,
.blog-detail-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.blog-meta-icon {
  font-size: 1rem;
  color: var(--primary-color);
}

/* Excerpt */
.blog-detail-excerpt {
  font-size: var(--heading6);
  color: var(--text-gray);
  line-height: 1.6;
  margin: 1.5rem 2rem;
  padding: 1rem 1.5rem;
  background-color: var(--bg-gray);
  border-left: 4px solid var(--primary-color);
  border-radius: 0 var(--border-small-radius) var(--border-small-radius) 0;
  font-style: italic;
}

/* Main Content Body */
.blog-detail-body {
  padding: 0 2rem 2rem;
  line-height: 1.8;
  font-size: var(--basefont);
  color: var(--text-color);
}

/* Sidebar */
.blog-detail-sidebar {
  align-self: start;
}

.blog-detail-meta-card {
  background-color: var(--white);
  border-radius: var(--border-medium-radius);
  box-shadow: var(--box-shadow-light);
  padding: 1.5rem;
}

.blog-detail-meta-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-gray);
}

.blog-detail-meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-light-gray);
}

.blog-detail-meta-item:last-of-type {
  border-bottom: none;
  margin-bottom: 1.5rem;
}

.blog-detail-meta-label {
  font-weight: 500;
  color: var(--text-gray);
  font-size: var(--smallfont);
}

.blog-detail-meta-value {
  color: var(--text-color);
  font-size: var(--smallfont);
}

.blog-detail-meta-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: uppercase;
}

.blog-detail-meta-status.published {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.blog-detail-meta-status.draft {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.blog-detail-meta-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.blog-detail-meta-action-btn {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: var(--border-small-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blog-detail-meta-action-btn.edit {
  background-color: var(--primary-color);
  color: var(--white);
}

.blog-detail-meta-action-btn.edit:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
}

.blog-detail-meta-action-btn.publish {
  background-color: #4caf50;
  color: white;
}

.blog-detail-meta-action-btn.publish:hover {
  background-color: #3d8b40;
  transform: translateY(-2px);
}

.blog-detail-meta-action-btn.unpublish {
  background-color: #ff9800;
  color: white;
}

.blog-detail-meta-action-btn.unpublish:hover {
  background-color: #f57c00;
  transform: translateY(-2px);
}

/* Rich Text Content Styling */
.html-content {
  font-family: var(--font-Poppins);
  color: var(--text-color);
}

.html-content h1,
.html-content h2,
.html-content h3,
.html-content h4,
.html-content h5,
.html-content h6 {
  color: var(--text-primary);
  margin-top: 1.5em;
  margin-bottom: 0.8em;
  font-weight: 600;
  line-height: 1.3;
}

.html-content h1 {
  font-size: var(--heading3);
}

.html-content h2 {
  font-size: var(--heading4);
}

.html-content h3 {
  font-size: var(--heading5);
}

.html-content h4,
.html-content h5,
.html-content h6 {
  font-size: var(--heading6);
}

.html-content p {
  margin-bottom: 1em;
}

.html-content a {
  color: var(--primary-color);
  text-decoration: underline;
  transition: color 0.2s;
}

.html-content a:hover {
  color: var(--primary-hover-color);
}

.html-content ul,
.html-content ol {
  margin-bottom: 1em;
  padding-left: 2em;
}

.html-content ul li,
.html-content ol li {
  margin-bottom: 0.5em;
}

.html-content blockquote {
  border-left: 4px solid var(--primary-light-bg);
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: var(--text-gray);
}

.html-content img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-small-radius);
  margin: 1em 0;
}

.html-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
}

.html-content table th,
.html-content table td {
  border: 1px solid var(--border-gray);
  padding: 0.5em;
}

.html-content table th {
  background-color: var(--bg-gray);
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .blog-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .blog-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .blog-actions {
    width: 100%;
  }

  .search-filter-container {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .search-input {
    grid-column: span 3;
  }

  .blog-form {
    padding: 15px;
  }

  /* Blog Detail Responsive */
  .blog-detail-layout {
    grid-template-columns: 1fr;
  }

  .blog-detail-sidebar {
    position: static;
    margin-top: 1.5rem;
  }

  .blog-detail-meta-card {
    position: relative;
  }

  .blog-detail-title {
    font-size: var(--heading3);
  }
}

@media (max-width: 768px) {
  .search-filter-container {
    grid-template-columns: 1fr 1fr;
    gap: 10px;
  }

  .search-input {
    grid-column: span 2;
  }

  .add-blog-btn {
    grid-column: span 2;
  }

  .blog-detail-container {
    padding: 0;
  }

  .blog-detail-layout {
    padding: 1rem;
    gap: 1rem;
  }

  .blog-detail-sticky-header {
    padding: 0.75rem 1rem;
  }

  .blog-detail-header-content {
    padding: 1.5rem 1.5rem 0.75rem;
  }

  .blog-detail-author-date {
    padding: 0 1.5rem 1rem;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .blog-detail-excerpt {
    margin: 1rem 1.5rem;
    padding: 0.75rem 1rem;
  }

  .blog-detail-body {
    padding: 0 1.5rem 1.5rem;
  }

  .blog-detail-title {
    font-size: var(--heading4);
    gap: 0.5rem;
  }

  .blog-title-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 576px) {
  .search-filter-container {
    grid-template-columns: 1fr;
  }

  .search-input {
    grid-column: span 1;
  }

  .add-blog-btn {
    grid-column: span 1;
  }

  .blog-detail-sticky-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
  }

  .blog-detail-actions {
    width: 100%;
    justify-content: space-between;
  }

  .blog-detail-layout {
    padding: 0.75rem;
  }

  .blog-detail-content {
    border-radius: var(--border-small-radius);
  }

  .blog-detail-header-content {
    padding: 1.25rem 1.25rem 0.75rem;
  }

  .blog-detail-author-date {
    padding: 0 1.25rem 0.75rem;
  }

  .blog-detail-excerpt {
    margin: 0.75rem 1.25rem;
    padding: 0.75rem;
    font-size: var(--basefont);
  }

  .blog-detail-body {
    padding: 0 1.25rem 1.25rem;
    font-size: var(--smallfont);
  }

  .blog-detail-title {
    font-size: var(--heading5);
  }

  .blog-detail-meta-card {
    padding: 1.25rem;
    border-radius: var(--border-small-radius);
  }

  .blog-detail-meta-title {
    font-size: var(--basefont);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
  }

  .blog-detail-meta-item {
    padding: 0.5rem 0;
  }

  .blog-detail-meta-actions {
    gap: 0.5rem;
    display: flex;
    flex-direction: row;
  }

  .blog-detail-meta-action-btn {
    padding: 0.6rem;
    font-size: var(--smallfont);
  }
}

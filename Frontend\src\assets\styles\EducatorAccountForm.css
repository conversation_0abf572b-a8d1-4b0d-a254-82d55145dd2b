.educator-form-container {
  background-color: var(--bg-gray);
  min-height: 100vh;
  width: 100%;
}

.educator-form-container .form-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.educator-form-container .form-header h1 {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--text-color);
}

.educator-form-container .back-button {
  background-color: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.educator-form-container .back-button:hover {
  background-color: var(--primary-light-color);
  color: white;
}

.educator-form-container .educator-form {
  background-color: var(--bg-white);
  border-radius: var(--border-large-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow-light);
  width: 100%;
  overflow: hidden;
}

/* Form Sections */
.educator-form-container .form-section {
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-gray);
  padding-bottom: 1.5rem;
}

.educator-form-container .form-section:last-of-type {
  border-bottom: none;
}

.educator-form-container .form-section h2 {
  font-size: var(--heading6);
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-gray);
}

/* Grid Layout */
.educator-form-container .form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

/* Form Groups */
.educator-form-container .form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.educator-form-container .form-group label {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

.educator-form-container .form-group input,
.educator-form-container .form-group select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  width: 100%;
  background-color: var(--bg-white);
  transition: all 0.3s ease;
}

.educator-form-container .form-group input:focus,
.educator-form-container .form-group select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.educator-form-container .form-group input:disabled,
.educator-form-container .form-group select:disabled {
  background-color: var(--bg-gray);
  color: var(--text-gray);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Error Messages */
.educator-form-container .error-message {
  color: #d32f2f;
  font-size: var(--smallfont);
  margin-top: 0.25rem;
}

/* Phone Input */
.educator-form-container .phone-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  overflow: hidden;
}

.educator-form-container .phone-input-wrapper .phone-prefix {
  padding: 0.75rem 1rem;
  background-color: var(--bg-gray);
  border-right: 1px solid var(--border-gray);
  font-size: var(--basefont);
  color: var(--text-color);
}

.educator-form-container .phone-input-wrapper input {
  flex: 1;
  border: none !important;
  border-radius: 0 !important;
}

/* Profile Section */
.educator-form-container .profile-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1.5rem;
  background-color: var(--bg-gray);
  border-radius: var(--border-small-radius);
}

.educator-form-container .profile-preview {
  flex-shrink: 0;
}

.educator-form-container .profile-preview-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid var(--bg-white);
  box-shadow: var(--box-shadow-light);
}

.educator-form-container .profile-placeholder {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-white);
  border-radius: 50%;
  box-shadow: var(--box-shadow-light);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23cccccc'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 50%;
}

.educator-form-container .profile-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.educator-form-container .upload-hint {
  font-size: var(--smallfont);
  color: var(--text-gray);
  margin: 0;
}

.educator-form-container .upload-photo-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  width: fit-content;
}

.educator-form-container .upload-photo-btn:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
}

/* Form Actions */
.educator-form-container .form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.educator-form-container .cancel-btn,
.educator-form-container .submit-btn {
  padding: 0.75rem 2rem;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.educator-form-container .cancel-btn {
  background-color: transparent;
  border: 1px solid var(--dark-gray);
  color: var(--text-gray);
}

.educator-form-container .submit-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.educator-form-container .cancel-btn:hover {
  background-color: var(--bg-gray);
}

.educator-form-container .submit-btn:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
}

/* Error messages */
.educator-form-container .error-message {
  color: #d32f2f;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: block;
}

.educator-form-container .general-error {
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: #ffebee;
  border-radius: 4px;
  text-align: center;
}

/* Disabled inputs */
.educator-form-container .disabled-input {
  background-color: var(--bg-gray) !important;
  color: var(--text-gray) !important;
  cursor: not-allowed !important;
  opacity: 0.8;
}

/* Credentials note */
.educator-form-container .credentials-note {
  background-color: #e8f4fd;
  border-left: 4px solid #2196f3;
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  border-radius: 4px;
}

.educator-form-container .credentials-note p {
  color: #0d47a1;
  font-size: var(--smallfont);
  margin: 0;
}

.educator-form-container .optional-label {
  font-size: 0.8rem;
  color: var(--text-gray);
  font-weight: normal;
  margin-left: 0.5rem;
}

/* Loading spinner */
.loading-spinner-small {
  display: inline-block;
  width: 1.2rem;
  height: 1.2rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Disabled buttons */
.educator-form-container .form-actions button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .educator-form-container .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .educator-form-container .educator-form {
    padding: 1.75rem;
  }
}

@media (max-width: 1024px) {
  .educator-form-container .educator-form {
    padding: 1.5rem;
  }

  .educator-form-container .form-header h1 {
    font-size: calc(var(--heading4) - 2px);
  }

  .educator-form-container .profile-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .educator-form-container .profile-actions {
    align-items: center;
  }
}

@media (max-width: 960px) {
  .educator-form-container .form-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.25rem;
  }

  .educator-form-container .form-section {
    padding-bottom: 1.5rem;
  }

  .educator-form-container .form-section h2 {
    font-size: calc(var(--heading6) - 1px);
  }

  .educator-form-container .form-group label {
    font-size: var(--smallfont);
  }

  .educator-form-container .form-group input,
  .educator-form-container .form-group select,
  .educator-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.7rem 0.9rem;
    font-size: calc(var(--basefont) - 1px);
  }
}

@media (max-width: 768px) {
  .educator-form-container .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .educator-form-container .educator-form {
    padding: 1.25rem;
    border-radius: var(--border-medium-radius);
  }

  .educator-form-container .form-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
  }

  .educator-form-container .form-section h2 {
    font-size: var(--basefont);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
  }

  .educator-form-container .form-header {
    margin-bottom: 1.5rem;
  }

  .educator-form-container .form-header h1 {
    font-size: var(--heading5);
  }

  .educator-form-container .form-group label {
    font-size: var(--smallfont);
  }

  .educator-form-container .form-group input,
  .educator-form-container .form-group select {
    padding: 0.7rem 0.9rem;
    font-size: var(--smallfont);
  }

  .educator-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.7rem 0.9rem;
    font-size: var(--smallfont);
  }

  .educator-form-container .profile-preview-image,
  .educator-form-container .profile-placeholder {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 576px) {
  .educator-form-container .educator-form {
    padding: 1rem;
    border-radius: var(--border-small-radius);
  }

  .educator-form-container .form-actions {
    flex-direction: row;
    gap: 0.75rem;
  }

  .educator-form-container .cancel-btn,
  .educator-form-container .submit-btn {
    width: 100%;
    padding: 0.7rem 1rem;
  }

  .educator-form-container .back-button {
    width: 36px;
    height: 36px;
    padding: 0px;
  }

  .educator-form-container .upload-photo-btn {
    padding: 0.7rem 1rem;
    font-size: var(--smallfont);
    width: 100%;
    justify-content: center;
  }

  .educator-form-container .form-section h2 {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .educator-form-container .form-header h1 {
    font-size: var(--heading6);
  }

  .educator-form-container .back-button {
    width: 32px;
    height: 32px;
  }

  .educator-form-container .profile-preview-image,
  .educator-form-container .profile-placeholder {
    width: 80px;
    height: 80px;
  }

  .educator-form-container .form-section {
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
  }

  .educator-form-container .form-group input,
  .educator-form-container .form-group select,
  .educator-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.6rem 0.8rem;
    font-size: var(--smallfont);
  }
}

@media (max-width: 380px) {
  .educator-form-container .educator-form {
    padding: 0.75rem;
  }

  .educator-form-container .form-header {
    margin-bottom: 1rem;
  }

  .educator-form-container .form-group {
    gap: 0.25rem;
  }

  .educator-form-container .form-group input,
  .educator-form-container .form-group select,
  .educator-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.5rem 0.7rem;
    font-size: var(--extrasmallfont);
  }

  .educator-form-container .form-section h2 {
    font-size: var(--extrasmallfont);
    margin-bottom: 0.75rem;
  }

  .educator-form-container .form-group label {
    font-size: var(--extrasmallfont);
  }

  .educator-form-container .profile-section {
    padding: 1rem;
  }
}

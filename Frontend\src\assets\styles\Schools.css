.schools-container {
  margin: 0 auto;
  min-height: 100vh;
}

/* Schools container specific styles */

/* Table styling */
.schools-container .table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  margin-bottom: 1rem;
}

.schools-container table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px; /* Ensures table doesn't get too compressed */
}

.schools-container th,
.schools-container td {
  padding: 0.75rem;
  text-align: left;
  vertical-align: middle;
  white-space: nowrap;
}

/* Owner cell styling */
.schools-container .owner-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  white-space: nowrap;
}

.schools-container .owner-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

/* Action buttons container */
.schools-container .action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.schools-container .action-button {
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: var(--border-small-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.schools-container .action-button.view {
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
}

.schools-container .action-button.edit {
  background-color: #ecfdf5;
  color: #059669;
}

.schools-container .action-button.delete {
  background-color: #fef2f2;
  color: #dc2626;
}

.schools-container .action-button.add {
  background-color: #e7eee5;
  color: #4abb66;
}

.schools-container .action-button:hover {
  transform: translateY(-1px);
}

.schools-container .action-button svg {
  width: 14px;
  height: 14px;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .schools-container {
    padding: 0 1.5rem;
  }

  .schools-container .search-filter-container {
    grid-template-columns: 3fr 2fr 2fr 2fr 1fr;
    gap: 0.75rem;
  }
}

@media (max-width: 1130px) and (min-width: 900.5px) {
  /* your styles here */
  .schools-container {
    padding: 0 1rem;
  }

  .schools-container .search-filter-container {
    grid-template-columns: 1fr 1fr 1fr;
    margin-bottom: 1rem;
    width: 100%;
    gap: 0.75rem;
  }

  .schools-container .create-account-btn {
    margin-left: auto;
  }
}

@media (max-width: 900px) {
  .topbar .user-name {
    font-weight: 600;
    font-size: var(--extrasmallfont);
    color: var(--text-color);
  }
  .topbar .dashboard-title h1 {
    font-size: var(--heading5);
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  .schools-container .search-filter-container {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .schools-container .search-input {
    grid-column: span 1;
  }

  .schools-container .create-account-btn {
    grid-column: span 1;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .schools-container {
    padding: 0 0.75rem;
  }

  .schools-container .schools-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .schools-container .search-filter-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    align-items: center;
  }

  .schools-container .search-input {
    grid-column: span 2;
    min-width: 100%;
  }

  .schools-container .filter-select {
    max-width: 100%;
  }

  .schools-container .create-account-btn {
    justify-self: end;
    max-width: fit-content;
  }
}

@media (max-width: 576px) {
  .schools-container {
    padding: 0 0.5rem;
  }

  .schools-container .search-filter-container {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .schools-container .search-input {
    grid-column: span 2;
    padding: 8px 12px;
  }

  .schools-container .filter-select {
    padding: 8px 12px;
  }

  .schools-container .create-account-btn {
    padding: 8px 16px;
    grid-column: span 2;
    justify-self: end;
  }
}

@media (max-width: 480px) {
  .schools-container {
    padding: 0 0.25rem;
  }

  .schools-container .search-filter-container {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .schools-container .search-input {
    grid-column: span 3;
  }

  .schools-container .filter-select {
    min-width: 0;
  }

  .schools-container .create-account-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .schools-container .owner-avatar {
    width: 24px;
    height: 24px;
  }
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-cell span {
  font-size: 14px;
  text-wrap-mode: nowrap;
  font-weight: 500;
}

.status-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  /* border: 2px solid transparent; */
  position: relative;
  flex-shrink: 0;
}

.status-indicator:before {
  content: "";
  position: absolute;
  top: -10px;
  right: -10px;
  bottom: -10px;
  left: -10px;
  border-radius: 50%;
  cursor: pointer;
}

.status-indicator.active {
  background-color: var(--primary-color);
  border-color: transparent;
}

.status-counts {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.count-item {
  font-size: var(--basefont);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.count-item strong {
  font-weight: 600;
  font-size: 1.1em;
}

@media (max-width: 768px) {
  .status-counts {
    gap: 1rem;
    justify-content: space-between;
    width: 100%;
  }

  .count-item {
    font-size: var(--smallfont);
  }
}

/* Add styles for school avatar */
.school-cell {
  display: flex;
  align-items: center;
  gap: 10px;
   overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
}

.school-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #eaeaea;
}

.school-avatar-placeholder {
  width: 40px;
  height: 40px;
  color: #ccc;
  border-radius: 50%;
  background-color: #f5f5f5;
  padding: 8px;
  box-sizing: border-box;
}

/* Modal styling */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-index-modal);
}

.modal-content {
  background-color: var(--bg-white);
  border-radius: var(--border-small-radius);
  padding: 20px;
  box-shadow: var(--box-shadow);
  max-height: 80vh;
  overflow-y: auto;
  animation: modal-fade-in 0.3s ease-out;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modal sizes */
.modal-small {
  width: 90%;
  max-width: 400px;
}

.modal-medium {
  width: 90%;
  max-width: 600px;
}

.modal-large {
  width: 90%;
  max-width: 800px;
}

/* Modal header */
.modal-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.modal-header h3 {
  margin: 0;
  font-size: var(--heading5);
  color: var(--text-color);
  flex-grow: 1;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: auto;
}

.close-btn:hover {
  color: var(--text-color);
}

/* Modal body */
.modal-body {
  margin-bottom: 20px;
  font-size: var(--basefont);
  color: var(--text-color);
  line-height: 1.5;
}

/* Modal footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Modal buttons */
.modal-button {
  padding: 8px 16px;
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s, transform 0.1s;
}

.modal-button:hover {
  transform: translateY(-1px);
}

.modal-button:active {
  transform: translateY(0);
}

/* Button variants */
.modal-button.primary {
  background-color: var(--primary-color);
  color: white;
}

.modal-button.primary:hover {
  background-color: var(--primary-hover-color);
}

.modal-button.secondary {
  background-color: var(--bg-gray);
  color: var(--text-color);
}

.modal-button.secondary:hover {
  background-color: var(--dark-gray);
}

.modal-button.danger {
  background-color: #dc3545;
  color: white;
}

.modal-button.danger:hover {
  background-color: #c82333;
}

.modal-button.success {
  background-color: #28a745;
  color: white;
}

.modal-button.success:hover {
  background-color: #218838;
}

.modal-button.warning {
  background-color: #ffc107;
  color: #212529;
}

.modal-button.warning:hover {
  background-color: #e0a800;
}

/* Modal icons */
.modal-icon {
  font-size: 24px;
  margin-right: 12px;
}

.modal-icon.success {
  color: #28a745;
}

.modal-icon.error {
  color: #dc3545;
}

.modal-icon.warning {
  color: #ffc107;
}

.modal-icon.info {
  color: var(--primary-color);
}

/* Modal types */
.modal-success .modal-header h3 {
  color: #28a745;
}

.modal-error .modal-header h3 {
  color: #dc3545;
}

.modal-warning .modal-header h3 {
  color: #ffc107;
}

.modal-info .modal-header h3 {
  color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    padding: 15px;
  }
  
  .modal-header h3 {
    font-size: var(--heading6);
  }
  
  .modal-body {
    font-size: var(--smallfont);
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .modal-button {
    width: 100%;
    margin-bottom: 8px;
  }
}

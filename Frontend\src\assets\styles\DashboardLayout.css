/* Dashboard container */
.dashboard-container {
  display: grid;
  grid-template-columns: 280px 1fr;

  background-color: var(--bg-gray);
  transition: grid-template-columns 0.3s ease;
}

/* Dashboard content area */
.dashboard-container .dashboard-content {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Main content area */
.dashboard-container .main-content {
  flex: 1;
  padding: 1.5vw;
  overflow-y: auto;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .dashboard-container {
    grid-template-columns: 240px 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    grid-template-columns: 1fr;
  }
}

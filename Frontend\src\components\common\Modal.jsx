import React, { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { IoClose } from 'react-icons/io5';
import { FaCheck, FaExclamationTriangle, FaInfoCircle, FaTimes } from 'react-icons/fa';
import '../../assets/styles/Modal.css';

/**
 * Reusable Modal component
 * 
 * @param {Object} props
 * @param {Boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when modal is closed
 * @param {String} props.title - Modal title
 * @param {React.ReactNode} props.children - Modal content
 * @param {String} props.type - Modal type (info, success, error, warning)
 * @param {Boolean} props.showCloseButton - Whether to show the close button
 * @param {Array} props.buttons - Array of button objects { text, onClick, variant }
 * @param {String} props.size - Modal size (small, medium, large)
 */
const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  type = 'info',
  showCloseButton = true,
  buttons = [],
  size = 'medium'
}) => {
  const modalRef = useRef(null);

  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && isOpen && onClose) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
      // Prevent scrolling of the body when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  // Close modal when clicking outside
  const handleOutsideClick = (event) => {
    if (modalRef.current && !modalRef.current.contains(event.target)) {
      onClose();
    }
  };

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  // Get icon based on type
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <FaCheck className="modal-icon success" />;
      case 'error':
        return <FaTimes className="modal-icon error" />;
      case 'warning':
        return <FaExclamationTriangle className="modal-icon warning" />;
      case 'info':
      default:
        return <FaInfoCircle className="modal-icon info" />;
    }
  };

  // Get size class
  const getSizeClass = () => {
    switch (size) {
      case 'small':
        return 'modal-small';
      case 'large':
        return 'modal-large';
      case 'medium':
      default:
        return 'modal-medium';
    }
  };

  return (
    <div className="modal-overlay" onClick={handleOutsideClick}>
      <div 
        className={`modal-content ${getSizeClass()} modal-${type}`} 
        ref={modalRef}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="modal-header">
          {getIcon()}
          <h3>{title}</h3>
          {showCloseButton && (
            <button className="close-btn" onClick={onClose}>
              <IoClose />
            </button>
          )}
        </div>
        <div className="modal-body">
          {children}
        </div>
        {buttons.length > 0 && (
          <div className="modal-footer">
            {buttons.map((button, index) => (
              <button
                key={index}
                className={`modal-button ${button.variant || 'primary'}`}
                onClick={button.onClick}
              >
                {button.text}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

Modal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  children: PropTypes.node,
  type: PropTypes.oneOf(['info', 'success', 'error', 'warning']),
  showCloseButton: PropTypes.bool,
  buttons: PropTypes.arrayOf(
    PropTypes.shape({
      text: PropTypes.string.isRequired,
      onClick: PropTypes.func.isRequired,
      variant: PropTypes.oneOf(['primary', 'secondary', 'danger', 'success', 'warning'])
    })
  ),
  size: PropTypes.oneOf(['small', 'medium', 'large'])
};

export default Modal;

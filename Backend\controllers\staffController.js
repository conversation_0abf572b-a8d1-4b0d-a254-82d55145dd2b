const User = require("../models/User");
const bcrypt = require("bcryptjs");
const AppError = require("../utils/appError");
const catchAsync = require("../utils/catchAsync");
const { formatPhoneNumber } = require("../utils/phoneUtils");

/**
 * Get all staff members
 * @route GET /api/admin/staff
 * @access Private (Admin only)
 */
exports.getStaffMembers = catchAsync(async (req, res, next) => {
  // Find the IIM Staff role
  const Role = require("../models/Role");
  const staffRole = await Role.findOne({ name: "IIM Staff" });

  // Find all users with the IIM Staff role (role="staff") or with the IIM Staff roleRef
  // Exclude users with role="admin" (Super Admin)
  const staffMembers = await User.find({
    $or: [{ role: "staff" }, { roleRef: staffRole ? staffRole._id : null }],
    role: { $ne: "admin" }, // Exclude users with role="admin"
  }).populate("roleRef");

  res.status(200).json({
    status: "success",
    results: staffMembers.length,
    data: staffMembers,
  });
});

/**
 * Get a staff member by ID
 * @route GET /api/admin/staff/:id
 * @access Private (Admin only)
 */
exports.getStaffMemberById = catchAsync(async (req, res, next) => {
  // Find the IIM Staff role
  const Role = require("../models/Role");
  const staffRole = await Role.findOne({ name: "IIM Staff" });

  // Find staff member
  const staffMember = await User.findById(req.params.id).populate("roleRef");

  // Check if the user exists
  if (!staffMember) {
    return next(new AppError("No staff member found with that ID", 404));
  }

  // Check if the user has a staff-related role
  // Only accept 'staff' role or IIM Staff roleRef, exclude admin role
  const isStaffRole =
    staffMember.role === "staff" ||
    (staffMember.roleRef &&
      staffMember.roleRef.toString() === (staffRole?._id?.toString() || ""));

  // Exclude users with admin role
  if (!isStaffRole || staffMember.role === "admin") {
    return next(new AppError("No staff member found with that ID", 404));
  }

  res.status(200).json({
    status: "success",
    data: staffMember,
  });
});

/**
 * Create a new staff member
 * @route POST /api/admin/staff
 * @access Private (Admin only)
 */
exports.createStaffMember = catchAsync(async (req, res, next) => {
  // Extract form data
  const { firstName, lastName, email, phoneNumber } = req.body;

  // Verify required fields
  if (!firstName || !lastName || !email || !phoneNumber) {
    return next(
      new AppError("First name, last name, email, and phone number are required fields", 400)
    );
  }

  // Check if user with the same email already exists
  const existingUser = await User.findOne({ email });
  if (existingUser) {
    return next(new AppError("A user with this email already exists", 400));
  }

  // Check if user with the same phone number already exists
  // Format the phone number for comparison
  const formattedPhoneNumber = formatPhoneNumber(phoneNumber);
  const existingPhoneUser = await User.findOne({ phoneNumber: formattedPhoneNumber });
  if (existingPhoneUser) {
    return next(
      new AppError("A user with this phone number already exists", 400)
    );
  }

  // Find the IIM Staff role
  const Role = require("../models/Role");
  const staffRole = await Role.findOne({ name: "IIM Staff" });

  if (!staffRole) {
    return next(
      new AppError(
        "IIM Staff role not found. Please run the seeder first.",
        500
      )
    );
  }

  // Check if a custom role is provided
  let roleRef = staffRole._id;
  if (req.body.roleId) {
    // Verify the role exists
    const customRole = await Role.findById(req.body.roleId);
    if (customRole) {
      roleRef = customRole._id;
    }
  }

  // Prepare staff member data
  const staffData = {
    firstName,
    lastName,
    name: `${firstName} ${lastName}`, // Keep for backward compatibility
    email,
    phoneNumber: formattedPhoneNumber, // Use the already formatted phone number
    role: "staff", // Fixed core role value for staff
    roleRef: roleRef, // Assign custom role if provided, otherwise use IIM Staff role
    status: req.body.status || 1, // Default to active
  };

  // Prepare profile data
  const profileData = {};

  // Get profile fields from form data - check both formats (with dot notation and without)
  if (req.body["profile.designation"] || req.body.designation)
    profileData.designation =
      req.body["profile.designation"] || req.body.designation;

  if (req.body["profile.department"] || req.body.department)
    profileData.department =
      req.body["profile.department"] || req.body.department;

  // Address field removed for staff members

  if (req.body["profile.state"] || req.body.state)
    profileData.state = req.body["profile.state"] || req.body.state;

  if (req.body["profile.zipcode"] || req.body.zipcode)
    profileData.zipcode = req.body["profile.zipcode"] || req.body.zipcode;

  // Add profile data if we have any fields
  if (Object.keys(profileData).length > 0) {
    staffData.profile = profileData;
  }

  // Handle avatar if uploaded
  if (req.file) {
    staffData.profile = staffData.profile || {};
    staffData.profile.avatar = `uploads/${req.file.filename}`;
  }

  // Create new staff member
  const staffMember = await User.create(staffData);

  res.status(201).json({
    status: "success",
    data: staffMember,
  });
});

/**
 * Update a staff member
 * @route PUT /api/admin/staff/:id
 * @access Private (Admin only)
 */
exports.updateStaffMember = catchAsync(async (req, res, next) => {
  const { firstName, lastName, email, phoneNumber } = req.body;

  // Find the IIM Staff role
  const Role = require("../models/Role");
  const staffRole = await Role.findOne({ name: "IIM Staff" });

  // Check if staff member exists
  const staffMember = await User.findById(req.params.id).populate("roleRef");

  // Check if the user exists
  if (!staffMember) {
    return next(new AppError("No staff member found with that ID", 404));
  }

  // Check if the user has a staff-related role
  // Only accept 'staff' role or IIM Staff roleRef, exclude admin role
  const isStaffRole =
    staffMember.role === "staff" ||
    (staffMember.roleRef &&
      staffMember.roleRef.toString() === (staffRole?._id?.toString() || ""));

  // Exclude users with admin role
  if (!isStaffRole || staffMember.role === "admin") {
    return next(new AppError("No staff member found with that ID", 404));
  }

  // Check if email is being changed and if it's already in use
  if (email && email !== staffMember.email) {
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return next(new AppError("A user with this email already exists", 400));
    }
  }

  // Check if phone number is being changed and if it's already in use
  if (phoneNumber && phoneNumber !== staffMember.phoneNumber) {
    // Format the phone number for comparison
    const formattedPhoneNumber = formatPhoneNumber(phoneNumber);
    const existingPhoneUser = await User.findOne({ phoneNumber: formattedPhoneNumber });
    if (existingPhoneUser && existingPhoneUser._id.toString() !== req.params.id) {
      return next(
        new AppError("A user with this phone number already exists", 400)
      );
    }
  }

  if (!staffRole) {
    return next(
      new AppError(
        "IIM Staff role not found. Please run the seeder first.",
        500
      )
    );
  }

  // Check if a custom role is provided
  let roleRef = staffRole._id;
  if (req.body.roleId) {
    // Verify the role exists
    const customRole = await Role.findById(req.body.roleId);
    if (customRole) {
      roleRef = customRole._id;
    }
  }

  // Prepare update data
  const updateData = {
    firstName: firstName || staffMember.firstName,
    lastName: lastName || staffMember.lastName,
    name: (firstName && lastName) ? `${firstName} ${lastName}` : staffMember.name,
    email: email || staffMember.email,
    // Format phone number if provided, otherwise keep existing (already formatted) phone number
    phoneNumber: phoneNumber ? formatPhoneNumber(phoneNumber) : staffMember.phoneNumber,
    // Keep the existing role value, don't update it
    roleRef: roleRef, // Use custom role if provided, otherwise use IIM Staff role
    status: req.body.status || staffMember.status,
  };

  // Prepare profile data
  const profileData = staffMember.profile || {};

  // Update profile fields if they exist in the request (check both formats)
  if (req.body["profile.designation"] || req.body.designation) {
    profileData.designation =
      req.body["profile.designation"] || req.body.designation;
  }

  if (req.body["profile.department"] || req.body.department) {
    profileData.department =
      req.body["profile.department"] || req.body.department;
  }

  // Address field removed for staff members

  if (req.body["profile.state"] || req.body.state)
    profileData.state = req.body["profile.state"] || req.body.state;

  if (req.body["profile.zipcode"] || req.body.zipcode)
    profileData.zipcode = req.body["profile.zipcode"] || req.body.zipcode;

  // Add profile data to update (make sure it's properly set as a full object)
  updateData.profile = profileData;

  // Handle avatar if uploaded
  if (req.file) {
    updateData.profile.avatar = `uploads/${req.file.filename}`;
  }

  // Update staff member
  const updatedStaffMember = await User.findByIdAndUpdate(
    req.params.id,
    updateData,
    { new: true, runValidators: true }
  ).populate("roleRef");

  res.status(200).json({
    status: "success",
    data: updatedStaffMember,
  });
});

/**
 * Delete a staff member
 * @route DELETE /api/admin/staff/:id
 * @access Private (Admin only)
 */
exports.deleteStaffMember = catchAsync(async (req, res, next) => {
  // Find the IIM Staff role
  const Role = require("../models/Role");
  const staffRole = await Role.findOne({ name: "IIM Staff" });

  // Check if staff member exists
  const staffMember = await User.findById(req.params.id).populate("roleRef");

  // Check if the user exists
  if (!staffMember) {
    return next(new AppError("No staff member found with that ID", 404));
  }

  // Check if the user has a staff-related role
  // Only accept 'staff' role or IIM Staff roleRef, exclude admin role
  const isStaffRole =
    staffMember.role === "staff" ||
    (staffMember.roleRef &&
      staffMember.roleRef.toString() === (staffRole?._id?.toString() || ""));

  // Exclude users with admin role
  if (!isStaffRole || staffMember.role === "admin") {
    return next(new AppError("No staff member found with that ID", 404));
  }

  // Soft delete - set status to 0 (inactive) instead of hard deleting
  staffMember.status = 0;
  await staffMember.save();

  res.status(200).json({
    status: "success",
    message: "Staff member deleted successfully",
  });
});

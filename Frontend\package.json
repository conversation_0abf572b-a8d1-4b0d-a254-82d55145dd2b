{"name": "abc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@reduxjs/toolkit": "^2.6.1", "@studio-freight/lenis": "^1.0.42", "@tailwindcss/vite": "^4.1.4", "@types/summernote": "^0.8.10", "axios": "^1.8.4", "i18next": "^25.0.2", "i18next-browser-languagedetector": "^8.0.5", "jquery": "^3.7.1", "prop-types": "^15.8.1", "react": "^19.0.0", "react-data-table-component": "^7.7.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.0", "react-toastify": "^11.0.5", "styled-components": "^6.1.17", "summernote": "^0.9.1", "tailwindcss": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}
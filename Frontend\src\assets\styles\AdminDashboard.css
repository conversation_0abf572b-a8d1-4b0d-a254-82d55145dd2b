.admin-dashboard {
  padding: 0px;
  background-color: var(--bg-gray);
  position: relative;
  min-height: 100vh;
}

/* Loading Overlay */
.admin-dashboard .loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.admin-dashboard .loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--bg-gray);
  border-top: 5px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Stats Cards */
.admin-dashboard .dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.admin-dashboard .stat-card {
  position: relative;
  border-radius: 16px;
  padding: 24px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  flex-direction: row-reverse;
  align-items: center;
}
.admin-dashboard .stat-card.courses {
  background-color: #fff4de;
  cursor: pointer;
}
.admin-dashboard .stat-card.schools {
  background-color: #ffecef;
  cursor: pointer;
}

.admin-dashboard .stat-card.educators {
  background-color: #e8ffef;
  cursor: pointer;
}

.admin-dashboard .stat-card.ongoing {
  background-color: #ffefef;
  cursor: pointer;
}

.admin-dashboard .stat-card.enrolled-users {
  background-color: #e6f7ff;
  cursor: pointer;
}

.admin-dashboard .stat-card.staff {
  background-color: #f0f5ff;
  cursor: pointer;
}

.admin-dashboard .stat-card.blogs {
  background-color: #f9f0ff;
  cursor: pointer;
}

.admin-dashboard .stat-card.roles {
  background-color: #fff0f6;
  cursor: pointer;
}

.admin-dashboard .stat-count {
  font-size: 28px;
  font-weight: 600;
  margin: 8px 0;
  color: #333;
}

.admin-dashboard .stat-title {
  font-size: 14px;
  color: #666;
}

.admin-dashboard .stat-icon1 {
  background-color: #fa5a7d;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.admin-dashboard .stat-icon2 {
  background-color: #3cd856;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.admin-dashboard .stat-icon3 {
  background-color: #ff947a;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.admin-dashboard .stat-icon4 {
  background-color: #36cfc9;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.admin-dashboard .stat-icon5 {
  background-color: #597ef7;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.admin-dashboard .stat-icon6 {
  background-color: #b37feb;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.admin-dashboard .stat-icon7 {
  background-color: #f759ab;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.admin-dashboard .icondesign1,
.admin-dashboard .icondesign2,
.admin-dashboard .icondesign3,
.admin-dashboard .icondesign4,
.admin-dashboard .icondesign5,
.admin-dashboard .icondesign6,
.admin-dashboard .icondesign7 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 80px;
  opacity: 0.1;
}

/* Section Header */
.admin-dashboard .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.admin-dashboard .section-title {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--text-color);
}

.admin-dashboard .header-actions {
  display: flex;
  gap: 12px;
}

.admin-dashboard .add-course-btn,
.admin-dashboard .view-all-btn {
  padding: 8px 16px;
  border-radius: var(--border-small-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-dashboard .add-course-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.admin-dashboard .view-all-btn {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.admin-dashboard .add-course-btn:hover {
  background-color: var(--primary-hover-color);
}

.admin-dashboard .view-all-btn:hover {
  background-color: var(--primary-light-bg);
}

/* Search and Filter Container */
.admin-dashboard .search-filter-container {
  display: grid;
  grid-template-columns: 6fr 3fr 2fr 2fr;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.admin-dashboard .search-input {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: var(--border-small-radius);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.admin-dashboard .search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.admin-dashboard .filter-select {
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: var(--border-small-radius);
  font-size: 14px;
  background-color: white;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s;
}

.admin-dashboard .filter-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

/* Course Info and Professor Info */
.admin-dashboard .course-info,
.admin-dashboard .professor-info {
  display: flex;
  align-items: center;
  gap: 12px;
   overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
}

.admin-dashboard .course-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: var(--border-small-radius);
  object-fit: cover;
}

.admin-dashboard .professor-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

/* Status Indicator */
.admin-dashboard .status-indicator {
  width: 36px;
  height: 20px;
  border-radius: 20px;
  background-color: #e5e7eb;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.admin-dashboard .status-indicator::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
}

.admin-dashboard .status-indicator.active {
  background-color: var(--primary-color);
}

.admin-dashboard .status-indicator.active::before {
  transform: translateX(16px);
}

/* Action Buttons */
.admin-dashboard .action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.admin-dashboard .action-btn,
.admin-dashboard .action-button {
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: var(--border-small-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-dashboard .action-btn.view,
.admin-dashboard .action-button.view {
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
}

.admin-dashboard .action-btn.edit,
.admin-dashboard .action-button.edit {
  background-color: #ecfdf5;
  color: #059669;
}

.admin-dashboard .action-btn.delete,
.admin-dashboard .action-button.delete {
  background-color: #fef2f2;
  color: #dc2626;
}

.admin-dashboard .action-btn.add,
.admin-dashboard .action-button.add {
  background-color: #e7eee5;
  color: #4abb66;
}

.admin-dashboard .action-btn:hover,
.admin-dashboard .action-button:hover {
  transform: translateY(-1px);
}

.admin-dashboard .action-btn svg,
.admin-dashboard .action-button svg {
  width: 14px;
  height: 14px;
}

/* DataTable Customization */
.admin-dashboard .rdt_Table {
  background-color: white;

  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-dashboard .rdt_TableHeader {
  display: none;
}

.admin-dashboard .rdt_TableHead {
  background-color: #f9fafb;
}

.admin-dashboard .rdt_TableHeadRow {
  border-bottom: 1px solid #e5e7eb;
}

.admin-dashboard .rdt_TableCol {
  padding: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.admin-dashboard .rdt_TableRow {
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s;
}

.admin-dashboard .rdt_TableRow:hover {
  background-color: #f9fafb;
}

.admin-dashboard .rdt_TableCell {
  padding: 16px;
  font-size: 14px;
  color: #374151;
}

.admin-dashboard .rdt_Pagination {
  border-top: 1px solid #e5e7eb;
  padding: 12px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .admin-dashboard .search-filter-container {
    grid-template-columns: 1fr 1fr;
  }

  .admin-dashboard .search-input {
    grid-column: span 2;
  }
}
@media (max-width: 900px) {
.admin-dashboard .stat-card{
  padding: 5px 24px;
}
  .statsCardcss{
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
}
@media (max-width: 768px) {
  .admin-dashboard {
    padding: 16px;
  }

  .admin-dashboard .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .admin-dashboard .header-actions {
    flex-direction: row;
  }

  .admin-dashboard .search-filter-container {
    grid-template-columns: 1fr;
  }

.admin-dashboard .stat-icon1,
.admin-dashboard .stat-icon2,
.admin-dashboard .stat-icon3,
.admin-dashboard .stat-icon4,
.admin-dashboard .stat-icon5,
.admin-dashboard .stat-icon6

 {

  width: 45px;
  height: 45px;
 
}
  .admin-dashboard .search-input {
    grid-column: span 1;
  }
  .admin-dashboard .stat-card {
 
  padding: 5px 24px;
 
}

}

@media (max-width: 640px) {
  .admin-dashboard .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

}

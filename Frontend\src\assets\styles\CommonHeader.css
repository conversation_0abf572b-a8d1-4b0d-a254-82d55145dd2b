/* Common Header Styles for Schools, Educators, and Staffs pages */
.page-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;

  width: 100%;
}

.page-header-top {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
  justify-content: space-between;
}

.page-title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.create-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-small-radius);
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
  margin-left: 1rem;
}

.create-button:hover {
  background-color: var(--primary-hover-color);
}

.filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  width: 100%;
}

.search-input {
  padding: 10px 16px;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-small-radius);
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
  width: 100%;
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.filter-select {
  padding: 10px 16px;
  border: 1px solid #e2e8f0;
  border-radius: var(--border-small-radius);
  background-color: white;
  font-size: 14px;
  outline: none;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  width: 100%;
}

.filter-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

/* Responsive styles */
@media (max-width: 1200px) {
  .filters-row {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.75rem;
  }
}

@media (max-width: 992px) {
  .filters-row {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .page-header-top {
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
  }

  .page-title {
    font-size: var(--heading5);
  }

  .create-button {
    margin-left: 1rem;
    font-size: 13px;
    padding: 8px 16px;
  }

  .filters-row {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }
}

@media (max-width: 576px) {
  .page-header-top {
    flex-direction: column;
    align-items: flex-start;
  }

  .create-button {
    margin-left: 0;
    margin-top: 0.5rem;
    width: 100%;
  }

  .filters-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .search-input,
  .filter-select {
    padding: 8px 12px;
    font-size: 13px;
  }
}

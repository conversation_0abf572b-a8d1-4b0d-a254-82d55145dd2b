/* ProgressBar Component Styles */
.progress-bar-component {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
}

.progress-bar-bg {
  width: 100%;
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 9999px;
  transition: width 0.5s ease-out;
}

.progress-bar-text {
  font-weight: 500;
  color: #374151;
  min-width: 42px;
  text-align: right;
}

.progress-bar-text-inside {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 0.5rem;
}

/* Color variations */
.progress-bar-fill.bg-blue-600 {
  background-color: #2563eb;
}

.progress-bar-fill.bg-green-500 {
  background-color: #10b981;
}

.progress-bar-fill.bg-yellow-500 {
  background-color: #f59e0b;
}

.progress-bar-fill.bg-red-500 {
  background-color: #ef4444;
}

.progress-bar-fill.bg-indigo-500 {
  background-color: #6366f1;
}

/* Size variations */
.progress-bar-bg.h-1\.5 {
  height: 0.375rem;
}

.progress-bar-bg.h-2\.5 {
  height: 0.625rem;
}

.progress-bar-bg.h-4 {
  height: 1rem;
}

/* Animation */
.progress-bar-fill.transition-all {
  transition: width 0.5s ease-out;
}

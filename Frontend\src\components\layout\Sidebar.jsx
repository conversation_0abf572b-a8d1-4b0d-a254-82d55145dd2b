import React, { useState, useEffect } from "react";
import { NavLink, useLocation } from "react-router-dom";
import bluelogo from "../..//assets/images/login_page/bluelogo.svg";
import {
  FaChalkboardTeacher,
  FaGraduationCap,
  FaHome,
  FaUsers,
  FaTimes,
  FaChevronDown,
  FaUniversity,
  FaUserTie,
  FaUserShield,
  FaIdBadge,
} from "react-icons/fa";
import { FaFilePen } from "react-icons/fa6";
import { hasLocalPermission, hasAnyLocalPermission } from "../../utils/localPermissions";
import "../../assets/styles/Sidebar.css";

/**
 * Sidebar component that auto-detects user role from localStorage
 */
const Sidebar = ({ isOpen, toggleSidebar }) => {
  const [userRole, setUserRole] = useState("admin");
  const [openDropdown, setOpenDropdown] = useState("");
  const location = useLocation();

  // Get user role from localStorage
  useEffect(() => {
    const user = localStorage.getItem("user");
    if (user) {
      try {
        const userData = JSON.parse(user);
        setUserRole(userData.role || "admin");

        // Log user permissions for debugging
        console.debug("Sidebar - User permissions:", {
          role: userData.role,
          roleRef: userData.roleRef || 'none',
          permissions: userData.permissions || {}
        });
      } catch (error) {
        console.error("Error parsing user data", error);
      }
    }
  }, []);

  // Generate menu items based on user role and permissions
  const getMenuItems = () => {
    // Common menu items - still check permissions
    const items = [];

    // Dashboard is always visible
    items.push({
      id: "dashboard",
      name: "Dashboard",
      icon: <FaHome className="menu-icon" />,
      path: `/dashboard/${getDashboardPath()}`,
      exact: true,
    });

    // Courses - visible if user has any course-related permission
    if (hasAnyLocalPermission([
      "view_courses", "create_course", "edit_course", "delete_course"
    ])) {
      items.push({
        id: "courses",
        name: "All Courses",
        icon: <FaGraduationCap className="menu-icon" />,
        path: `/dashboard/${getDashboardPath()}/courses`,
      });
    }

    // Blogs - visible if user has any blog-related permission
    if (hasAnyLocalPermission([
      "view_blogs", "create_blog", "edit_blog", "delete_blog"
    ])) {
      items.push({
        id: "blogs",
        name: "Blogs",
        icon: <FaFilePen className="menu-icon" />,
        path: `/dashboard/${getDashboardPath()}/blogs`,
      });
    }

    // Users dropdown menu - check permissions for each submenu item
    const userSubmenu = [];

    // Schools submenu - visible if user has any school-related permission
    if (hasAnyLocalPermission([
      "view_schools", "create_school", "edit_school", "delete_school"
    ])) {
      userSubmenu.push({
        id: "universities",
        name: "Schools",
        path: `/dashboard/admin/schools`,
        icon: <FaUniversity />,
      });
    }

    // Educators submenu - visible if user has any educator-related permission
    if (hasAnyLocalPermission([
      "view_educators", "create_educator", "edit_educator", "delete_educator"
    ])) {
      userSubmenu.push({
        id: "educators",
        name: "Educators",
        path: `/dashboard/admin/educators`,
        icon: <FaUserTie />,
      });
    }

    // My Team submenu - only visible for admin and staff roles
    if (userRole === "admin" || userRole === "staff") {
      userSubmenu.push({
        id: "staffs",
        name: "My Team",
        path: `/dashboard/admin/staffs`,
        icon: <FaIdBadge />,
      });
    }

    // Add the Users dropdown if it has any submenu items
    if (userSubmenu.length > 0) {
      // Find the right position to insert (after courses if it exists)
      const coursesIndex = items.findIndex(item => item.id === "courses");
      const insertIndex = coursesIndex !== -1 ? coursesIndex + 1 : items.length;

      items.splice(insertIndex, 0, {
        id: "users",
        name: "Users",
        icon: <FaUsers className="menu-icon" />,
        isDropdown: true,
        submenu: userSubmenu,
      });
    }

    // Special case for university role - add educators link if they have permission
    if (userRole === "university" && hasAnyLocalPermission([
      "view_educators", "create_educator", "edit_educator", "delete_educator"
    ])) {
      // Find the right position to insert (after courses if it exists)
      const coursesIndex = items.findIndex(item => item.id === "courses");
      const insertIndex = coursesIndex !== -1 ? coursesIndex + 1 : items.length;

      // Only add if not already added via the dropdown
      if (!items.some(item => item.id === "users")) {
        items.splice(insertIndex, 0, {
          id: "educators",
          name: "Educators",
          icon: <FaChalkboardTeacher className="menu-icon" />,
          path: `/dashboard/school/educators`,
        });
      }
    }

    // Add Role/Permission tab for admin only
    if (userRole === "admin" && hasLocalPermission("manage_roles")) {
      items.push({
        id: "role-permission",
        name: "Role & Permission",
        icon: <FaUserShield className="menu-icon" />,
        path: `/dashboard/admin/role-permission`,
      });
    }

    return items;
  };

  // Get the correct dashboard path based on role
  const getDashboardPath = () => {
    if (userRole === "admin") {
      return "admin"; // Super Admin -> admin
    } else if (userRole === "staff") {
      return "admin"; // IIM Staff -> admin (same dashboard as Super Admin)
    } else if (userRole === "university") {
      return "school"; // School Admin -> university
    } else if (userRole === "educator") {
      return "tutor"; // Educator -> educator
    } else {
      return "tutor"; // Default fallback
    }
  };

  // Toggle dropdown menu
  const toggleDropdown = (id) => {
    setOpenDropdown(openDropdown === id ? "" : id);
  };

  // Check if a submenu item is active
  const isSubmenuActive = (submenuItems) => {
    return submenuItems.some(
      (item) =>
        location.pathname === item.path ||
        location.pathname.startsWith(item.path + "/")
    );
  };

  // Auto-open dropdown if a submenu item is active
  useEffect(() => {
    const menuItems = getMenuItems();
    const activeDropdown = menuItems.find(
      (item) => item.isDropdown && isSubmenuActive(item.submenu)
    );

    if (activeDropdown) {
      setOpenDropdown(activeDropdown.id);
    }
  }, [location.pathname, userRole]); // userRole is included because getMenuItems depends on it

  return (
    <>
      <div className="sidebar-layout-parent">
        {/* Mobile sidebar overlay */}
        {isOpen && (
          <div className="sidebar-overlay" onClick={toggleSidebar}></div>
        )}

        {/* Sidebar container */}
        <div className={`sidebar ${isOpen ? "open" : ""}`}>
          {/* Logo & close button container for mobile */}
          <div className="sidebar-header">
            <img src={bluelogo} alt="" />
            <h1 className="sidebar-logo">IIM Ahmedabad</h1>
            <button className="close-sidebar-btn" onClick={toggleSidebar}>
              <FaTimes />
            </button>
          </div>

          {/* Navigation menu */}
          <nav className="sidebar-nav">
            <ul className="sidebar-menu">
              {getMenuItems().map((item) => (
                <li
                  key={item.id}
                  className={`menu-item ${item.isDropdown ? "dropdown-menu" : ""
                    }`}
                >
                  {item.isDropdown ? (
                    <>
                      <button
                        className={`dropdown-toggle ${openDropdown === item.id ? "open" : ""
                          } ${isSubmenuActive(item.submenu) ? "active" : ""}`}
                        onClick={() => toggleDropdown(item.id)}
                      >
                        <div className="menu-icon-wrapper">
                          {item.icon}
                          <span className="menu-text">{item.name}</span>
                        </div>
                        <FaChevronDown className="dropdown-arrow" size={12} />
                      </button>
                      <div
                        className={`dropdown-content ${openDropdown === item.id ? "open" : ""
                          }`}
                      >
                        {item.submenu.map((subItem) => (
                          <div key={subItem.id} className="submenu-item">
                            <NavLink
                              to={subItem.path}
                              className={({ isActive }) =>
                                `submenu-link ${isActive ? "active" : ""}`
                              }
                              onClick={toggleSidebar}
                            >
                              {subItem.icon && (
                                <span className="submenu-icon">
                                  {subItem.icon}
                                </span>
                              )}
                              <span className="submenu-text">
                                {subItem.name}
                              </span>
                            </NavLink>
                          </div>
                        ))}
                      </div>
                    </>
                  ) : (
                    <NavLink
                      to={item.path}
                      className={({ isActive }) =>
                        `menu-link ${isActive ? "active" : ""}`
                      }
                      end={item.exact}
                      onClick={toggleSidebar}
                    >
                      {item.icon}
                      <span className="menu-text">{item.name}</span>
                    </NavLink>
                  )}
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;

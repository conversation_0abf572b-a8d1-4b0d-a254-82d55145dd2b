.staff-form-container {
  background-color: var(--bg-gray);
  min-height: 100vh;
  width: 100%;
}

.staff-form-container .form-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.staff-form-container .form-header h1 {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--text-color);
}

.staff-form-container .back-button {
  background-color: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.staff-form-container .back-button:hover {
  background-color: var(--primary-light-color);
  color: white;
}

.staff-form-container .staff-form {
  background-color: var(--bg-white);
  border-radius: var(--border-large-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow-light);
  width: 100%;
  overflow: hidden;
}

/* Form Sections */
.staff-form-container .form-section {
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-gray);
  padding-bottom: 1.5rem;
}

.staff-form-container .form-section:last-of-type {
  border-bottom: none;
}

.staff-form-container .form-section h2 {
  font-size: var(--heading6);
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-gray);
}

/* Grid Layout */
.staff-form-container .form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

/* Form Groups */
.staff-form-container .form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.staff-form-container .form-group label {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

.staff-form-container .form-group input,
.staff-form-container .form-group select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  width: 100%;
  background-color: var(--bg-white);
  transition: all 0.3s ease;
}

.staff-form-container .form-group input:focus,
.staff-form-container .form-group select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.staff-form-container .form-group input:disabled,
.staff-form-container .form-group select:disabled {
  background-color: var(--bg-gray);
  color: var(--text-gray);
  cursor: not-allowed;
  opacity: 0.7;
}

.staff-form-container .form-group input.error {
  border-color: #d32f2f;
}

/* Error Messages */
.staff-form-container .error-message {
  color: #d32f2f;
  font-size: var(--smallfont);
  margin-top: 0.25rem;
}

/* Phone Input */
.staff-form-container .phone-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  overflow: hidden;
}

.staff-form-container .phone-input-wrapper .phone-prefix {
  padding: 0.75rem 1rem;
  background-color: var(--bg-gray);
  border-right: 1px solid var(--border-gray);
  font-size: var(--basefont);
  color: var(--text-color);
}

.staff-form-container .phone-input-wrapper input {
  flex: 1;
  border: none !important;
  border-radius: 0 !important;
}

/* Profile Section */
.staff-form-container .profile-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1.5rem;
  background-color: var(--bg-gray);
  border-radius: var(--border-small-radius);
}

.staff-form-container .profile-preview {
  flex-shrink: 0;
}

.staff-form-container .profile-preview-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid var(--bg-white);
  box-shadow: var(--box-shadow-light);
}

.staff-form-container .profile-placeholder {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-white);
  border-radius: 50%;
  box-shadow: var(--box-shadow-light);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23cccccc'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 50%;
}

.staff-form-container .profile-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.staff-form-container .upload-hint {
  font-size: var(--smallfont);
  color: var(--text-gray);
  margin: 0;
}

.staff-form-container .upload-photo-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  width: fit-content;
}

.staff-form-container .upload-photo-btn:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
}

.staff-form-container .remove-photo-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.staff-form-container .remove-photo-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-2px);
}

.icon-margin-right {
  margin-right: 0.5rem;
}

/* Form Actions */
.staff-form-container .form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.staff-form-container .cancel-btn,
.staff-form-container .submit-btn {
  padding: 0.75rem 2rem;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.staff-form-container .cancel-btn {
  background-color: transparent;
  border: 1px solid var(--dark-gray);
  color: var(--text-gray);
}

.staff-form-container .submit-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.staff-form-container .cancel-btn:hover {
  background-color: var(--bg-gray);
}

.staff-form-container .submit-btn:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
}

/* Error messages */
.staff-form-container .error-message {
  color: #d32f2f;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: block;
}

.staff-form-container .general-error {
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: #ffebee;
  border-radius: 4px;
  text-align: center;
}

.staff-form-container .disabled-input {
  background-color: var(--bg-gray) !important;
  color: var(--text-gray) !important;
  cursor: not-allowed;
}

.staff-form-container .credentials-note {
  background-color: var(--bg-gray);
  padding: 1rem;
  border-radius: var(--border-small-radius);
  margin-top: 1rem;
}

.staff-form-container .credentials-note p {
  color: var(--text-gray);
  font-size: var(--smallfont);
  margin-bottom: 0.5rem;
}

.staff-form-container .optional-label {
  color: var(--text-gray);
  font-size: var(--smallfont);
  margin-left: 0.25rem;
  font-weight: 400;
}

.loading-spinner-small {
  width: 24px;
  height: 24px;
  border: 3px solid var(--bg-gray);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.staff-form-container .form-actions button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .staff-form-container .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .staff-form-container .staff-form {
    padding: 1.75rem;
  }
}

@media (max-width: 1024px) {
  .staff-form-container .staff-form {
    padding: 1.5rem;
  }

  .staff-form-container .form-header h1 {
    font-size: calc(var(--heading4) - 2px);
  }

  .staff-form-container .profile-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .staff-form-container .profile-actions {
    align-items: center;
  }
}

@media (max-width: 960px) {
  .staff-form-container .form-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.25rem;
  }

  .staff-form-container .form-section {
    padding-bottom: 1.5rem;
  }

  .staff-form-container .form-section h2 {
    font-size: calc(var(--heading6) - 1px);
  }

  .staff-form-container .form-group label {
    font-size: var(--smallfont);
  }

  .staff-form-container .form-group input,
  .staff-form-container .form-group select,
  .staff-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.7rem 0.9rem;
    font-size: calc(var(--basefont) - 1px);
  }
}

@media (max-width: 768px) {
  .staff-form-container .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .staff-form-container .staff-form {
    padding: 1.25rem;
    border-radius: var(--border-medium-radius);
  }

  .staff-form-container .form-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
  }

  .staff-form-container .form-section h2 {
    font-size: var(--basefont);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
  }

  .staff-form-container .form-header {
    margin-bottom: 1.5rem;
  }

  .staff-form-container .form-header h1 {
    font-size: var(--heading5);
  }

  .staff-form-container .form-group label {
    font-size: var(--smallfont);
  }

  .staff-form-container .form-group input,
  .staff-form-container .form-group select {
    padding: 0.7rem 0.9rem;
    font-size: var(--smallfont);
  }

  .staff-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.7rem 0.9rem;
    font-size: var(--smallfont);
  }

  .staff-form-container .profile-preview-image,
  .staff-form-container .profile-placeholder {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 576px) {
  .staff-form-container .staff-form {
    padding: 1rem;
    border-radius: var(--border-small-radius);
  }

  .staff-form-container .form-actions {
    flex-direction: row;
    gap: 0.75rem;
  }

  .staff-form-container .cancel-btn,
  .staff-form-container .submit-btn {
    width: 100%;
    padding: 0.7rem 1rem;
    justify-content: center;
  }

  .staff-form-container .back-button {
    width: 36px;
    height: 36px;
    padding: 0px;
  }

  .staff-form-container .upload-photo-btn,
  .staff-form-container .remove-photo-btn {
    padding: 0.7rem 1rem;
    font-size: var(--smallfont);
    width: 100%;
    justify-content: center;
  }

  .staff-form-container .form-section h2 {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .staff-form-container .form-header h1 {
    font-size: var(--heading6);
  }

  .staff-form-container .back-button {
    width: 32px;
    height: 32px;
  }

  .staff-form-container .profile-preview-image,
  .staff-form-container .profile-placeholder {
    width: 80px;
    height: 80px;
  }

  .staff-form-container .form-section {
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
  }

  .staff-form-container .form-group input,
  .staff-form-container .form-group select,
  .staff-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.6rem 0.8rem;
    font-size: var(--smallfont);
  }
}

@media (max-width: 380px) {
  .staff-form-container .staff-form {
    padding: 0.75rem;
  }

  .staff-form-container .form-header {
    margin-bottom: 1rem;
  }

  .staff-form-container .form-group {
    gap: 0.25rem;
  }

  .staff-form-container .form-group input,
  .staff-form-container .form-group select,
  .staff-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.5rem 0.7rem;
    font-size: var(--extrasmallfont);
  }

  .staff-form-container .form-section h2 {
    font-size: var(--extrasmallfont);
    margin-bottom: 0.75rem;
  }

  .staff-form-container .form-group label {
    font-size: var(--extrasmallfont);
  }

  .staff-form-container .profile-section {
    padding: 1rem;
  }
}

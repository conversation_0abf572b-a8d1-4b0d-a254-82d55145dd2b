import React, { useState, useMemo } from "react";
import DataTable from "react-data-table-component";
import "../assets/styles/DataTable.css";

const DataTableComponent = ({
  columns,
  data,
  title = "",
  searchPlaceholder = "Search...",
  showSearch = true,
  pagination = true,
}) => {
  const [filterText, setFilterText] = useState("");

  // Process columns to convert boolean attributes to strings
  const processedColumns = useMemo(() => {
    return columns.map((column) => {
      const newColumn = { ...column };

      // Convert boolean 'center' attribute to string
      if (newColumn.center === true) {
        newColumn.center = "true";
      }

      // Convert boolean 'right' attribute to string
      if (newColumn.right === true) {
        newColumn.right = "true";
      }

      return newColumn;
    });
  }, [columns]);

  const filteredItems = useMemo(() => {
    if (!filterText) return data;

    return data.filter((item) => {
      return Object.keys(item).some((key) => {
        const value = item[key];
        if (value !== null && value !== undefined) {
          return String(value).toLowerCase().includes(filterText.toLowerCase());
        }
        return false;
      });
    });
  }, [data, filterText]);

  // Use the provided search placeholder
  const finalSearchPlaceholder = searchPlaceholder;



  return (
    <div className="data-table-container">
      <DataTable
        title={title}
        columns={processedColumns}
        data={filteredItems}
        pagination={pagination}
        persistTableHead
        highlightOnHover
        pointerOnHover
        responsive
        noDataComponent="No results found"
      />
    </div>
  );
};

export default DataTableComponent;

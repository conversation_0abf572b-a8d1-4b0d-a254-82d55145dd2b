<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <defs>
    <linearGradient id="gradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="10%" stop-color="hsl(210, 100%, 50%)" />
      <stop offset="90%" stop-color="hsl(240, 100%, 50%)" />
    </linearGradient>
  </defs>
  <rect x="0" y="0" width="200" height="200" fill="url(#gradient)" />
  <circle cx="100" cy="70" r="40" fill="#ffffff" opacity="0.7" />
  <circle cx="100" cy="200" r="80" fill="#ffffff" opacity="0.7" />
</svg> 
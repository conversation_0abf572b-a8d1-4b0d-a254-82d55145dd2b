const express = require("express");
const staffController = require("../controllers/staffController");
const { protect, restrictTo, hasPermission } = require("../middleware/auth");
const upload = require("../middleware/upload");
const router = express.Router();
const { PERMISSIONS } = require("../utils/permissions");

// Apply authentication middleware to all routes
router.use(protect);

// Staff routes with multer for handling file uploads
router
  .route("/")
  .get(hasPermission("view_users"), staffController.getStaffMembers)
  .post(restrictTo("admin"), upload.single("avatar"), staffController.createStaffMember);

router
  .route("/:id")
  .get(hasPermission("view_users"), staffController.getStaffMemberById)
  .put(hasPermission("edit_user"), upload.single("avatar"), staffController.updateStaffMember)
  .delete(restrictTo("admin"), staffController.deleteStaffMember);

module.exports = router;

import React, { useState, useEffect } from "react";
import { FaUpload, FaImage, FaTimes } from "react-icons/fa";

const CourseInfoStep = ({
    courseData,
    updateCourseData,
    thumbnailFile,
    setThumbnailFile,
    thumbnailPreview,
    setThumbnailPreview
}) => {
    // Local state for form fields
    const [localData, setLocalData] = useState({
        title: courseData.title || "",
        shortDescription: courseData.shortDescription || "",
        language: courseData.language || "en"
    });

    // Update local state when courseData changes
    useEffect(() => {
        setLocalData({
            title: courseData.title || "",
            shortDescription: courseData.shortDescription || "",
            language: courseData.language || "en"
        });
    }, [courseData]);

    // Update thumbnail preview when courseData changes
    useEffect(() => {
        if (courseData.thumbnail && !thumbnailPreview) {
            // If we have a thumbnail URL but no preview, set the preview
            setThumbnailPreview(courseData.thumbnail);
        }
    }, [courseData.thumbnail]);

    // Handle form field changes
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setLocalData(prev => ({ ...prev, [name]: value }));
        updateCourseData({ [name]: value });
    };

    // Handle thumbnail upload
    const handleThumbnailChange = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        if (file.type.startsWith("image/")) {
            setThumbnailFile(file);
            const previewUrl = URL.createObjectURL(file);
            setThumbnailPreview(previewUrl);
            // Don't update courseData.thumbnail here as we'll handle it in the parent
        } else {
            alert("Please select an image file for the thumbnail");
        }
    };

    // Remove thumbnail
    const removeThumbnail = () => {
        setThumbnailFile(null);
        setThumbnailPreview("");
        updateCourseData({ thumbnail: "" });
    };

    return (
        <div className="course-info-step">
            <h2>Course Information</h2>
            <p className="step-description">
                Start by providing basic information about your course. This helps students understand what they'll learn.
            </p>

            <div className="form-section">
                <div className="form-group">
                    <label htmlFor="title">
                        Course Title <span className="required">*</span>
                    </label>
                    <input
                        type="text"
                        id="title"
                        name="title"
                        value={localData.title}
                        onChange={handleInputChange}
                        placeholder="Enter a descriptive title for your course"
                        className="input-field"
                    />
                    <p className="field-hint">
                        A great title is clear, specific, and addresses what students will learn (100 characters max).
                    </p>
                </div>

                <div className="form-group">
                    <label htmlFor="shortDescription">
                        Course Description <span className="required">*</span>
                    </label>
                    <textarea
                        id="shortDescription"
                        name="shortDescription"
                        value={localData.shortDescription}
                        onChange={handleInputChange}
                        placeholder="Provide a short description that explains what your course covers"
                        rows={5}
                        className="textarea-field"
                    ></textarea>
                    <p className="field-hint">
                        Summarize what students will learn in your course. Highlight key points, technologies, or skills they'll gain.
                    </p>
                </div>

                <div className="form-group">
                    <label htmlFor="language">Course Language</label>
                    <select
                        id="language"
                        name="language"
                        value={localData.language}
                        onChange={handleInputChange}
                        className="select-field"
                    >
                        <option value="en">English</option>
                        <option value="hi">Hindi</option>
                    </select>
                </div>
            </div>

            <div className="thumbnail-section">
                <label>
                    Course Thumbnail <span className="required">*</span>
                </label>
                <p className="field-hint">
                    Upload an engaging image that represents your course. Recommended size: 1280x720 pixels (16:9 ratio).
                </p>

                <div className="thumbnail-container">
                    {thumbnailPreview ? (
                        <div className="thumbnail-preview">
                            <img src={thumbnailPreview} alt="Course thumbnail preview" />
                            <button
                                type="button"
                                className="remove-thumbnail"
                                onClick={removeThumbnail}
                            >
                                <FaTimes />
                            </button>
                        </div>
                    ) : (
                        <div className="thumbnail-upload">
                            <input
                                type="file"
                                id="thumbnail"
                                accept="image/*"
                                onChange={handleThumbnailChange}
                                className="hidden-input"
                            />
                            <label htmlFor="thumbnail" className="upload-label">
                                <FaImage className="upload-icon" />
                                <span>Choose Image</span>
                                <p className="upload-hint">
                                    Click to browse or drop image here
                                </p>
                            </label>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default CourseInfoStep;
import React, { useState } from 'react';
import { 
  showAlert, 
  showInfoAlert, 
  showSuccessAlert, 
  showErrorAlert, 
  showWarningAlert, 
  showConfirm 
} from '../../utils/alertService';

/**
 * Test component for the Modal component
 */
const ModalTest = () => {
  const [confirmResult, setConfirmResult] = useState(null);

  const handleShowAlert = () => {
    showAlert('This is a basic alert message');
  };

  const handleShowInfoAlert = () => {
    showInfoAlert('This is an information message', 'Information');
  };

  const handleShowSuccessAlert = () => {
    showSuccessAlert('Operation completed successfully!', 'Success');
  };

  const handleShowErrorAlert = () => {
    showErrorAlert('An error occurred while processing your request.', 'Error');
  };

  const handleShowWarningAlert = () => {
    showWarningAlert('This action may have consequences.', 'Warning');
  };

  const handleShowConfirm = async () => {
    const result = await showConfirm('Are you sure you want to proceed?', 'warning', 'Confirmation');
    setConfirmResult(result ? 'Confirmed' : 'Cancelled');
  };

  return (
    <div className="modal-test-container" style={{ padding: '20px' }}>
      <h2>Modal Component Test</h2>
      <p>Click the buttons below to test different types of modals:</p>
      
      <div className="button-group" style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
        <button onClick={handleShowAlert} className="modal-button primary">
          Basic Alert
        </button>
        <button onClick={handleShowInfoAlert} className="modal-button primary">
          Info Alert
        </button>
        <button onClick={handleShowSuccessAlert} className="modal-button success">
          Success Alert
        </button>
        <button onClick={handleShowErrorAlert} className="modal-button danger">
          Error Alert
        </button>
        <button onClick={handleShowWarningAlert} className="modal-button warning">
          Warning Alert
        </button>
        <button onClick={handleShowConfirm} className="modal-button secondary">
          Confirmation Dialog
        </button>
      </div>
      
      {confirmResult && (
        <div className="result-display" style={{ marginTop: '20px', padding: '10px', border: '1px solid #ccc', borderRadius: '4px' }}>
          <p>Confirmation result: <strong>{confirmResult}</strong></p>
        </div>
      )}
    </div>
  );
};

export default ModalTest;

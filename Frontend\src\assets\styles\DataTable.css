.data-table-container {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 2rem;
}

.data-table-container .search-container {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  width: 100%;
  flex-wrap: wrap;
  gap: 1rem;
}

.data-table-container .search-input {
  padding: 10px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  width: 100%;
  max-width: 350px;
  outline: none;
  transition: border-color 0.2s;
}
.data-table-container .hZInOG svg {
  top: 50%;
  transform: translateY(-50%);
}

@media (max-width: 576px) {
  .data-table-container .search-input {
    max-width: 100%;
  }
}

.data-table-container .search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

/* Override react-data-table-component styles */
.data-table-container .rdt_TableHeader {
  padding: 1.5rem 1.5rem 0.5rem !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
}

.data-table-container .rdt_TableHeadRow {
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
  font-weight: 600 !important;
  color: #1e293b !important;
}

.data-table-container .rdt_TableRow {
  border-bottom: 1px solid #e2e8f0 !important;
  color: #334155 !important;
  transition: background-color 0.2s;
}

.data-table-container .rdt_TableRow:hover {
  background-color: #f1f5f9 !important;
}

.data-table-container .rdt_Pagination {
  border-top: 1px solid #e2e8f0 !important;
  color: #334155 !important;
}

/* Status indicator styles */
.data-table-container .status-indicator {
  display: inline-block;
  width: 36px;
  height: 20px;
  border-radius: 20px;
  background-color: #e2e8f0;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s;
}

.data-table-container .status-indicator::after {
  content: "";
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  transition: transform 0.3s, background-color 0.3s;
}

.data-table-container .status-indicator.active {
  background-color: var(--primary-color);
}

.data-table-container .status-indicator.active::after {
  transform: translateX(16px);
}

/* Action buttons */
.data-table-container .action-button {
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.data-table-container .action-button.view {
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
}

.data-table-container .action-button.edit {
  background-color: #ecfdf5;
  color: #059669;
}

.data-table-container .action-button.delete {
  background-color: #fef2f2;
  color: #dc2626;
}

.data-table-container .action-button.add {
  background-color: #e7eee5;
  color: #4abb66;
}

.data-table-container .action-button:hover {
  transform: translateY(-1px);
}

.data-table-container .action-button svg {
  width: 14px;
  height: 14px;
}

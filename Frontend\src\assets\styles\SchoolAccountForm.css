.school-form-container {
  background-color: var(--bg-gray);
  min-height: 100vh;
  width: 100%;
}

.school-form-container .form-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.school-form-container .form-header h1 {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--text-color);
}

.school-form-container .back-button {
  background-color: var(--bg-white);
  border: 1px solid var(--border-gray);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.school-form-container .back-button:hover {
  background-color: var(--primary-light-color);
  color: white;
}

.school-form-container .school-form {
  background-color: var(--bg-white);
  border-radius: var(--border-large-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow-light);
  width: 100%;
  overflow: hidden;
}

/* Form Sections */
.school-form-container .form-section {
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--border-gray);
  padding-bottom: 1.5rem;
}

.school-form-container .form-section:last-of-type {
  border-bottom: none;
}

.school-form-container .form-section h2 {
  font-size: var(--heading6);
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-gray);
}

/* Grid Layout */
.school-form-container .form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

/* Form Groups */
.school-form-container .form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.school-form-container .form-group label {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

.school-form-container .form-group input,
.school-form-container .form-group select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  width: 100%;
  background-color: var(--bg-white);
  transition: all 0.3s ease;
}

.school-form-container .form-group input:focus,
.school-form-container .form-group select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.school-form-container .form-group input:disabled,
.school-form-container .form-group select:disabled {
  background-color: var(--bg-gray);
  color: var(--text-gray);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Phone Input */
.school-form-container .phone-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  overflow: hidden;
}

.school-form-container .phone-input-wrapper .phone-prefix {
  padding: 0.75rem 1rem;
  background-color: var(--bg-gray);
  border-right: 1px solid var(--border-gray);
  font-size: var(--basefont);
  color: var(--text-color);
}

.school-form-container .phone-input-wrapper input {
  flex: 1;
  border: none !important;
  border-radius: 0 !important;
}

/* Profile Section */
.school-form-container .profile-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1.5rem;
  background-color: var(--bg-gray);
  border-radius: var(--border-small-radius);
}

.school-form-container .profile-preview {
  flex-shrink: 0;
}

.school-form-container .profile-preview-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
  border: 3px solid var(--bg-white);
  box-shadow: var(--box-shadow-light);
}

.school-form-container .profile-placeholder {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-white);
  border-radius: 50%;
  box-shadow: var(--box-shadow-light);
}

.school-form-container .placeholder-icon {
  font-size: 80px;
  color: var(--dark-gray);
}

.school-form-container .profile-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.school-form-container .upload-hint {
  font-size: var(--smallfont);
  color: var(--text-gray);
  margin: 0;
}

.school-form-container .upload-photo-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  width: fit-content;
}

.school-form-container .upload-photo-btn:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
}

/* Form Actions */
.school-form-container .form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.school-form-container .cancel-btn,
.school-form-container .submit-btn {
  padding: 0.75rem 2rem;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.school-form-container .cancel-btn {
  background-color: transparent;
  border: 1px solid var(--dark-gray);
  color: var(--text-gray);
}

.school-form-container .submit-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.school-form-container .cancel-btn:hover {
  background-color: var(--bg-gray);
}

.school-form-container .submit-btn:hover {
  background-color: var(--primary-hover-color);
  transform: translateY(-2px);
}

/* Login information notice */
.login-info-notice {
  background-color: #e6f2ff;
  border-left: 4px solid #1976d2;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .school-form-container .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .school-form-container .school-form {
    padding: 1.75rem;
  }
}

@media (max-width: 1024px) {
  .school-form-container .school-form {
    padding: 1.5rem;
  }

  .school-form-container .form-header h1 {
    font-size: calc(var(--heading4) - 2px);
  }

  .school-form-container .profile-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .school-form-container .profile-actions {
    align-items: center;
  }
}

@media (max-width: 960px) {
  .school-form-container .form-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.25rem;
  }

  .school-form-container .form-section {
    padding-bottom: 1.5rem;
  }

  .school-form-container .form-section h2 {
    font-size: calc(var(--heading6) - 1px);
  }
}

@media (max-width: 768px) {
  .school-form-container .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .school-form-container .school-form {
    padding: 1.25rem;
    border-radius: var(--border-medium-radius);
  }

  .school-form-container .form-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
  }

  .school-form-container .form-section h2 {
    font-size: var(--basefont);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
  }

  .school-form-container .form-header {
    margin-bottom: 1.5rem;
  }

  .school-form-container .form-header h1 {
    font-size: var(--heading5);
  }

  .school-form-container .form-group label {
    font-size: var(--smallfont);
  }

  .school-form-container .form-group input,
  .school-form-container .form-group select {
    padding: 0.7rem 0.9rem;
    font-size: var(--smallfont);
  }

  .school-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.7rem 0.9rem;
    font-size: var(--smallfont);
  }

  .school-form-container .profile-preview-image,
  .school-form-container .profile-placeholder {
    width: 100px;
    height: 100px;
  }

  .school-form-container .placeholder-icon {
    font-size: 60px;
  }
}

@media (max-width: 576px) {
  .school-form-container .school-form {
    padding: 1rem;
    border-radius: var(--border-small-radius);
  }

  .school-form-container .form-actions {
    display: flex;
    gap: 0.75rem;
    flex-direction: row;
  }

  .school-form-container .cancel-btn,
  .school-form-container .submit-btn {
    width: 100%;
    padding: 0.7rem 1rem;
  }

  .school-form-container .back-button {
    width: 36px;
    height: 36px;
    padding: 0px;
  }

  .school-form-container .upload-photo-btn {
    padding: 0.7rem 1rem;
    font-size: var(--smallfont);
    width: 100%;
    justify-content: center;
  }

  .school-form-container .form-section h2 {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .school-form-container .form-header h1 {
    font-size: var(--heading6);
  }

  .school-form-container .back-button {
    width: 32px;
    height: 32px;
  }

  .school-form-container .profile-preview-image,
  .school-form-container .profile-placeholder {
    width: 80px;
    height: 80px;
  }

  .school-form-container .placeholder-icon {
    font-size: 50px;
  }

  .school-form-container .form-section {
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
  }

  .school-form-container .form-group input,
  .school-form-container .form-group select,
  .school-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.6rem 0.8rem;
    font-size: var(--smallfont);
  }
}

@media (max-width: 380px) {
  .school-form-container .school-form {
    padding: 0.75rem;
  }

  .school-form-container .form-header {
    margin-bottom: 1rem;
  }

  .school-form-container .form-group {
    gap: 0.25rem;
  }

  .school-form-container .form-group input,
  .school-form-container .form-group select,
  .school-form-container .phone-input-wrapper .phone-prefix {
    padding: 0.5rem 0.7rem;
    font-size: var(--extrasmallfont);
  }

  .school-form-container .form-section h2 {
    font-size: var(--extrasmallfont);
    margin-bottom: 0.75rem;
  }

  .school-form-container .form-group label {
    font-size: var(--extrasmallfont);
  }

  .school-form-container .profile-section {
    padding: 1rem;
  }
}

/* Add styles for file upload */
.upload-button {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  background-color: #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
  font-size: 14px;
  color: #333;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
}

.upload-button:hover {
  background-color: #e0e0e0;
}

.hidden-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Profile image preview styles */
.profile-preview-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 50%;
}

.profile-placeholder {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 50%;
}

.placeholder-icon {
  font-size: 80px;
  color: #cccccc;
}

.profile-placeholder p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

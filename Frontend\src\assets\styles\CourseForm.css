.course-form-container {
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 2rem;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.course-form-container .form-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
}

.course-form-container .back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-light);
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.course-form-container .back-button:hover {
  background-color: var(--border-gray);
}

.course-form-container h1 {
  font-size: var(--heading-medium);
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.course-form-container .form-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-gray);
}

.course-form-container .form-section h2 {
  font-size: var(--heading-small);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1.5rem;
}

.course-form-container .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.course-form-container .form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.course-form-container .form-group.full-width {
  grid-column: span 2;
}

.course-form-container .form-group label {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
}

.course-form-container .form-group input,
.course-form-container .form-group select,
.course-form-container .form-group textarea {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-gray);
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--bg-white);
  transition: border-color 0.3s;
}

.course-form-container .form-group input:focus,
.course-form-container .form-group select:focus,
.course-form-container .form-group textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.course-form-container .form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.course-form-container .error-message {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 0.25rem;
}

.course-form-container .attachments-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

.course-form-container .attachment-preview {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-gray);
}

.course-form-container .preview-media {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-form-container .remove-attachment {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--error-color);
  font-size: 12px;
}

.course-form-container .upload-box {
  width: 100px;
  height: 100px;
  border: 2px dashed var(--border-gray);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.course-form-container .upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  color: var(--text-light);
  font-size: var(--smallfont);
}

.course-form-container .form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.course-form-container .cancel-btn,
.course-form-container .submit-btn {
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

.course-form-container .cancel-btn {
  background-color: var(--bg-white);
  color: var(--text-color);
  border: 1px solid var(--border-gray);
}

.course-form-container .cancel-btn:hover {
  background-color: var(--bg-light);
}

.course-form-container .submit-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.course-form-container .submit-btn:hover {
  background-color: var(--primary-dark);
}

.course-form-container .submit-btn:disabled {
  background-color: var(--disabled-color);
  cursor: not-allowed;
}

.course-form-container .loading-spinner-small {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .course-form-container {
    padding: 1.5rem;
  }

  .course-form-container .form-row {
    grid-template-columns: 1fr;
  }

  .course-form-container .form-group.full-width {
    grid-column: span 1;
  }
}

.educators-container {
  margin: 0 auto;
  position: relative;
  min-height: 100vh;
}

/* Loading Overlay */
.educators-container .loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.educators-container .loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--bg-gray);
  border-top: 5px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Educators container specific styles */

/* Professor cell styling */
.educators-container .professor-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
   overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
}

.educators-container .professor-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.educators-container .professor-avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: #aaa;
  background-color: #f0f0f0;
}

/* Status cell styling */
.educators-container .status-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.educators-container .status-cell span {
  font-size: 14px;
  font-weight: 500;
}

/* Action buttons container */
.educators-container .action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.educators-container .action-button {
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: var(--border-small-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.educators-container .action-button.view {
  background-color: var(--primary-light-bg);
  color: var(--primary-color);
}

.educators-container .action-button.edit {
  background-color: #ecfdf5;
  color: #059669;
}

.educators-container .action-button.delete {
  background-color: #fef2f2;
  color: #dc2626;
}

.educators-container .action-button.add {
  background-color: #e7eee5;
  color: #4abb66;
}

.educators-container .action-button:hover {
  transform: translateY(-1px);
}

.educators-container .action-button svg {
  width: 14px;
  height: 14px;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .educators-container {
    padding: 0 1.5rem;
  }

  .educators-container .search-filter-container {
    grid-template-columns: 3fr 4fr 1fr 1fr;
    gap: 0.75rem;
  }
}

@media (max-width: 1130px) and (min-width: 900.5px) {
  /* your styles here */
  .educators-container {
    padding: 0 1rem;
  }

  .educators-container .search-filter-container {
    grid-template-columns: 1fr 1fr;
    margin-bottom: 1rem;
    width: 100%;
  }
  .educators-container.create-account-btn {
    margin-left: auto;
  }
}
@media (max-width: 900px) {
  .topbar .user-name {
    font-weight: 600;
    font-size: var(--extrasmallfont);
    color: var(--text-color);
  }
  .topbar .dashboard-title h1 {
    font-size: var(--heading5);
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  .educators-container .search-filter-container {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .educators-container .search-input {
    grid-column: span 1;
  }

  .educators-container .create-account-btn {
    grid-column: span 1;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .educators-container .educators-header {
    flex-direction: column;
    align-items: stretch;
  }

  .educators-container .search-filter-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0.75rem;
    align-items: center;
  }

  .educators-container .filters {
    margin-top: 0.5rem;
  }

  .educators-container .search-input {
    grid-column: span 3;
    min-width: 100%;
  }
  .educators-container .filter-select {
    max-width: 100%;
  }

  .educators-container.create-account-btn {
    justify-self: end;
    max-width: fit-content;
  }
}

@media (max-width: 576px) {
  .educators-container {
    padding: 0 0.5rem;
  }

  .educators-container .search-filter-container {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }
  .educators-container .search-input {
    grid-column: span 2;
    padding: 8px 12px;
  }

  .educators-container .filter-select {
    padding: 8px 12px;
  }

  .educators-container .create-account-btn {
    padding: 8px 16px;
    grid-column: span 2;
    justify-self: end;
  }
}

@media (max-width: 480px) {
  .educators-container {
    padding: 0 0.25rem;
  }

  .educators-container .search-filter-container {
    grid-template-columns: 2fr 1fr;
    gap: 0.5rem;
  }

  .educators-container .search-input {
    grid-column: span 3;
  }

  .educators-container .filter-select {
    min-width: 0;
  }
  .educators-container .create-account-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .educators-container .owner-avatar {
    width: 24px;
    height: 24px;
  }
}
